<?php

namespace App\Http\Controllers\SupportTeam;

use App\Http\Controllers\Controller;
use App\Models\Session;
use App\Models\StudentRecord;
use App\Models\Payment;
use App\Models\PaymentRecord;
use App\Repositories\StudentRepo;
use App\Repositories\MyClassRepo;
use App\Helpers\Qs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SessionController extends Controller
{
    protected $student, $my_class;

    public function __construct(StudentRepo $student, MyClassRepo $my_class)
    {
        $this->middleware('teamSA', ['except' => ['changeSession', 'getSessions']]);
        $this->middleware('super_admin', ['only' => ['destroy']]);
        
        $this->student = $student;
        $this->my_class = $my_class;
    }

    /**
     * Afficher la liste des sessions
     */
    public function index()
    {
        $data['sessions'] = Session::getAllSorted();
        return view('pages.support_team.sessions.index', $data);
    }

    /**
     * Créer une nouvelle session
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|unique:sessions,name',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'description' => 'nullable|string',
        ]);

        Session::create($request->all());

        return back()->with('flash_success', 'Session créée avec succès');
    }

    /**
     * Modifier une session
     */
    public function update(Request $request, $id)
    {
        $session = Session::findOrFail($id);
        
        $request->validate([
            'name' => 'required|unique:sessions,name,' . $id,
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'description' => 'nullable|string',
        ]);

        $session->update($request->all());

        return back()->with('flash_success', 'Session modifiée avec succès');
    }

    /**
     * Supprimer une session
     */
    public function destroy($id)
    {
        $session = Session::findOrFail($id);
        
        // Vérifier s'il y a des données liées
        $hasData = StudentRecord::where('session', $session->name)->exists() ||
                   Payment::where('year', $session->name)->exists();
        
        if ($hasData) {
            return back()->with('flash_danger', 'Impossible de supprimer cette session car elle contient des données');
        }

        $session->delete();
        return back()->with('flash_success', 'Session supprimée avec succès');
    }

    /**
     * Changer la session active
     */
    public function changeSession(Request $request)
    {
        $sessionName = $request->session_name;
        
        // Vérifier que la session existe
        $session = Session::where('name', $sessionName)->first();
        if (!$session) {
            return response()->json(['success' => false, 'message' => 'Session non trouvée']);
        }

        // Stocker dans la session utilisateur
        session(['selected_school_year' => $sessionName]);

        return response()->json([
            'success' => true, 
            'message' => 'Session changée vers ' . $sessionName,
            'session_name' => $sessionName
        ]);
    }

    /**
     * Obtenir toutes les sessions pour le dropdown
     */
    public function getSessions()
    {
        $sessions = Session::getAllSorted();
        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        
        return response()->json([
            'sessions' => $sessions,
            'current_session' => $currentSession
        ]);
    }

    /**
     * Définir une session comme active par défaut
     */
    public function setActive($id)
    {
        $session = Session::findOrFail($id);
        $session->setAsActive();

        return back()->with('flash_success', 'Session ' . $session->name . ' définie comme active');
    }
}
