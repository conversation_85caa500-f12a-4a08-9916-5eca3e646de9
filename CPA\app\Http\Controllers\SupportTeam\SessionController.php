<?php

namespace App\Http\Controllers\SupportTeam;

use App\Http\Controllers\Controller;
use App\Models\Session;
use App\Models\StudentRecord;
use App\Models\Payment;
use App\Models\PaymentRecord;
use App\Repositories\StudentRepo;
use App\Repositories\MyClassRepo;
use App\Helpers\Qs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SessionController extends Controller
{
    protected $student, $my_class;

    public function __construct(StudentRepo $student, MyClassRepo $my_class)
    {
        $this->middleware('teamSA', ['except' => ['changeSession', 'getSessions']]);
        $this->middleware('super_admin', ['only' => ['destroy']]);

        $this->student = $student;
        $this->my_class = $my_class;
    }

    /**
     * Afficher la liste des sessions
     */
    public function index()
    {
        $data['sessions'] = Session::getAllSorted();
        return view('pages.support_team.sessions.index', $data);
    }

    /**
     * Créer une nouvelle session
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|unique:sessions,name',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'description' => 'nullable|string',
        ]);

        Session::create($request->all());

        return back()->with('flash_success', 'Session créée avec succès');
    }

    /**
     * Modifier une session
     */
    public function update(Request $request, $id)
    {
        $session = Session::findOrFail($id);
        
        $request->validate([
            'name' => 'required|unique:sessions,name,' . $id,
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'description' => 'nullable|string',
        ]);

        $session->update($request->all());

        return back()->with('flash_success', 'Session modifiée avec succès');
    }

    /**
     * Supprimer une session
     */
    public function destroy($id)
    {
        $session = Session::findOrFail($id);
        
        // Vérifier s'il y a des données liées
        $hasData = StudentRecord::where('session', $session->name)->exists() ||
                   Payment::where('year', $session->name)->exists();
        
        if ($hasData) {
            return back()->with('flash_danger', 'Impossible de supprimer cette session car elle contient des données');
        }

        $session->delete();
        return back()->with('flash_success', 'Session supprimée avec succès');
    }

    /**
     * Changer la session active
     */
    public function changeSession(Request $request)
    {
        $sessionName = $request->session_name;
        
        // Vérifier que la session existe
        $session = Session::where('name', $sessionName)->first();
        if (!$session) {
            return response()->json(['success' => false, 'message' => 'Session non trouvée']);
        }

        // Stocker dans la session utilisateur
        session(['selected_school_year' => $sessionName]);

        return response()->json([
            'success' => true, 
            'message' => 'Session changée vers ' . $sessionName,
            'session_name' => $sessionName
        ]);
    }

    /**
     * Obtenir toutes les sessions pour le dropdown
     */
    public function getSessions()
    {
        $sessions = Session::getAllSorted();
        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        
        return response()->json([
            'sessions' => $sessions,
            'current_session' => $currentSession
        ]);
    }

    /**
     * Définir une session comme active par défaut
     */
    public function setActive($id)
    {
        $session = Session::findOrFail($id);
        $session->setAsActive();

        return back()->with('flash_success', 'Session ' . $session->name . ' définie comme active');
    }

    /**
     * Afficher l'interface de réinscription
     */
    public function reenrollment()
    {
        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        $currentSessionObj = Session::where('name', $currentSession)->first();

        $data['current_session'] = $currentSession;
        $data['previous_session'] = $currentSessionObj ? $currentSessionObj->getPreviousSession() : null;
        $data['my_classes'] = $this->my_class->all();
        $data['sessions'] = Session::getAllSorted();

        return view('pages.support_team.sessions.reenrollment', $data);
    }

    /**
     * Rechercher les élèves à réinscrire
     */
    public function searchStudents(Request $request)
    {
        $previousSession = $request->previous_session;
        $classId = $request->class_id;
        $sectionId = $request->section_id;

        $students = StudentRecord::where('session', $previousSession)
            ->where('my_class_id', $classId)
            ->where('section_id', $sectionId)
            ->where('grad', 0)
            ->with(['user', 'my_class', 'section'])
            ->get();

        // Vérifier lesquels sont déjà réinscrits
        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        foreach ($students as $student) {
            $student->already_enrolled = StudentRecord::where('user_id', $student->user_id)
                ->where('session', $currentSession)
                ->exists();
        }

        return response()->json([
            'students' => $students,
            'count' => $students->count()
        ]);
    }

    /**
     * Réinscrire les élèves sélectionnés
     */
    public function reenrollStudents(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|array',
            'new_class_id' => 'required|exists:my_classes,id',
            'new_section_id' => 'required|exists:sections,id',
            'previous_session' => 'required|string',
        ]);

        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        $enrolledCount = 0;
        $skippedCount = 0;
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($request->student_ids as $studentId) {
                // Vérifier si l'élève n'est pas déjà réinscrit
                $existingRecord = StudentRecord::where('user_id', $studentId)
                    ->where('session', $currentSession)
                    ->first();

                if ($existingRecord) {
                    $skippedCount++;
                    continue;
                }

                // Récupérer l'ancien enregistrement
                $oldRecord = StudentRecord::where('user_id', $studentId)
                    ->where('session', $request->previous_session)
                    ->first();

                if (!$oldRecord) {
                    $errors[] = "Élève ID $studentId non trouvé dans la session précédente";
                    continue;
                }

                // Créer le nouvel enregistrement
                $newRecord = StudentRecord::create([
                    'user_id' => $studentId,
                    'my_class_id' => $request->new_class_id,
                    'section_id' => $request->new_section_id,
                    'session' => $currentSession,
                    'adm_no' => $oldRecord->adm_no,
                    'my_parent_id' => $oldRecord->my_parent_id,
                    'dorm_id' => $oldRecord->dorm_id,
                    'dorm_room_no' => $oldRecord->dorm_room_no,
                    'house' => $oldRecord->house,
                    'age' => $oldRecord->age,
                    'year_admitted' => $oldRecord->year_admitted,
                ]);

                // Créer les enregistrements de paiement pour la nouvelle session
                $this->createPaymentRecords($studentId, $request->new_class_id, $currentSession);

                $enrolledCount++;
            }

            DB::commit();

            $message = "Réinscription terminée: $enrolledCount élève(s) réinscrit(s)";
            if ($skippedCount > 0) {
                $message .= ", $skippedCount élève(s) déjà inscrit(s)";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'enrolled' => $enrolledCount,
                'skipped' => $skippedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la réinscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Créer les enregistrements de paiement pour un élève
     */
    private function createPaymentRecords($studentId, $classId, $session)
    {
        // Récupérer les paiements pour cette classe et cette session
        $payments = Payment::where('year', $session)
            ->where(function($query) use ($classId) {
                $query->where('my_class_id', $classId)
                      ->orWhereNull('my_class_id');
            })
            ->get();

        foreach ($payments as $payment) {
            PaymentRecord::create([
                'student_id' => $studentId,
                'payment_id' => $payment->id,
                'year' => $session,
                'amt_paid' => 0,
                'balance' => $payment->amount,
                'paid' => 0,
            ]);
        }
    }
}
