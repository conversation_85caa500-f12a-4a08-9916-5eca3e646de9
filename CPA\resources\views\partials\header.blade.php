<div id="page-header" class="page-header page-header-light">
    <div class="page-header-content header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-plus-circle2 mr-2"></i> <span class="font-weight-semibold">@yield('page_title')</span></h4>
            <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
        </div>

        <div class="header-elements d-none">
            <div class="d-flex justify-content-center align-items-center">
                <!-- Sélecteur d'année scolaire -->
                <div class="dropdown mr-3">
                    <button type="button" class="btn btn-link btn-float text-default dropdown-toggle" data-toggle="dropdown">
                        <i class="icon-calendar text-primary"></i>
                        <span class="font-weight-semibold">Ann<PERSON> Scolaire: <span class="badge badge-primary" id="current-session-badge">{{ $current_session ?? Qs::getCurrentSession() }}</span></span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" id="session-dropdown">
                        <div class="dropdown-header">Changer d'année scolaire</div>
                        <div class="dropdown-divider"></div>
                        <!-- Les options seront chargées via AJAX -->
                        <div class="text-center p-2">
                            <i class="icon-spinner2 spinner"></i> Chargement...
                        </div>
                    </div>
                </div>

                @if(Qs::userIsSuperAdmin())
                <a href="{{ route('settings') }}" class="btn btn-link btn-float text-default">
                    <i class="icon-cog text-primary"></i>
                    <span>Paramètres</span>
                </a>
                @endif
            </div>
        </div>
    </div>

    {{--Breadcrumbs--}}
    {{--<div class="breadcrumb-line breadcrumb-line-light header-elements-md-inline">
        <div class="d-flex">
            <div class="breadcrumb">
                <a href="index.html" class="breadcrumb-item"><i class="icon-home2 mr-2"></i> Home</a>
                <a href="form_select2.html" class="breadcrumb-item">Forms</a>
                <span class="breadcrumb-item active">Select2 selects</span>
            </div>

            <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
        </div>

        <div class="header-elements d-none">
            <div class="breadcrumb justify-content-center">
                <a href="#" class="breadcrumb-elements-item">
                    <i class="icon-comment-discussion mr-2"></i>
                    Support
                </a>

                <div class="breadcrumb-elements-item dropdown p-0">
                    <a href="#" class="breadcrumb-elements-item dropdown-toggle" data-toggle="dropdown">
                        <i class="icon-gear mr-2"></i>
                        Settings
                    </a>

                    <div class="dropdown-menu dropdown-menu-right">
                        <a href="#" class="dropdown-item"><i class="icon-user-lock"></i> Account security</a>
                        <a href="#" class="dropdown-item"><i class="icon-statistics"></i> Analytics</a>
                        <a href="#" class="dropdown-item"><i class="icon-accessibility"></i> Accessibility</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item"><i class="icon-gear"></i> All settings</a>
                    </div>
                </div>
            </div>
        </div>
    </div>--}}
</div>
