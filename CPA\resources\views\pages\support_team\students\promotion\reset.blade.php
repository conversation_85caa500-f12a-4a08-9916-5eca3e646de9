@extends('layouts.master')
@section('page_title', 'Gérer les promotions')
@section('content')

    {{-- Actions rapides --}}
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">Réinscription Intelligente</h6>
                    <p class="card-text text-muted">Réinscrire des élèves avec promotion automatique</p>
                    <a href="{{ route('students.smart_reenrollment') }}" class="btn btn-primary">
                        <i class="icon-users-plus mr-2"></i>Nouvelle Réinscription
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">Réinitialiser les Promotions</h6>
                    <p class="card-text text-muted">Annuler toutes les promotions de la session</p>
                    <button id="promotion-reset-all" class="btn btn-danger">
                        <i class="icon-reset mr-2"></i>Réinitialiser Tout
                    </button>
                </div>
            </div>
        </div>
    </div>

{{-- Gérer les promotions --}}
    <div class="card">
        <div class="card-header header-elements-inline">
            <h5 class="card-title font-weight-bold">
                <i class="icon-graduation mr-2"></i>Promotions Enregistrées
                <small class="d-block text-muted mt-1">
                    Étudiants promus de la session <span class="text-danger">{{ $old_year }}</span> à la session <span class="text-success">{{ $new_year }}</span>
                </small>
            </h5>
            <div class="header-elements">
                <span class="badge badge-primary badge-pill">{{ $promotions->count() }} promotion(s)</span>
            </div>
        </div>

        <div class="card-body">
            @if($promotions->count() > 0)
            <div class="table-responsive">
            <table id="promotions-list" class="table table-striped table-hover datatable-button-html5-columns">
                <thead class="bg-light">
                <tr>
                    <th width="50">N°</th>
                    <th width="60">Photo</th>
                    <th>Nom de l'Élève</th>
                    <th>Classe d'Origine</th>
                    <th>Classe de Destination</th>
                    <th width="100">Statut</th>
                    <th width="120">Actions</th>
                </tr>
                </thead>
                <tbody>
                @foreach($promotions->sortBy('fc.name')->sortBy('student.name') as $p)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            <img class="rounded-circle" style="height: 40px; width: 40px;"
                                 src="{{ $p->student->photo }}" alt="photo">
                        </td>
                        <td>
                            <span class="font-weight-semibold">{{ $p->student->name }}</span>
                        </td>
                        <td>
                            <span class="badge badge-secondary">{{ $p->fc->name }}</span>
                            <small class="d-block text-muted">{{ $p->fs->name }}</small>
                        </td>
                        <td>
                            <span class="badge badge-primary">{{ $p->tc->name }}</span>
                            <small class="d-block text-muted">{{ $p->ts->name }}</small>
                        </td>
                        <td>
                            @if($p->status === 'P')
                                <span class="badge badge-success">
                                    <i class="icon-checkmark3 mr-1"></i>Promu
                                </span>
                            @elseif($p->status === 'D')
                                <span class="badge badge-warning">
                                    <i class="icon-cross mr-1"></i>Non promu
                                </span>
                            @else
                                <span class="badge badge-info">
                                    <i class="icon-graduation mr-1"></i>Diplômé
                                </span>
                            @endif
                        </td>
                        <td class="text-center">
                            <button data-id="{{ $p->id }}" class="btn btn-sm btn-outline-danger promotion-reset"
                                    title="Réinitialiser cette promotion">
                                <i class="icon-reset"></i>
                            </button>
                            <form id="promotion-reset-{{ $p->id }}" method="post" action="{{ route('students.promotion_reset', $p->id) }}">
                                @csrf @method('DELETE')
                            </form>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            </div>
            @else
            <div class="text-center py-5">
                <i class="icon-graduation text-muted" style="font-size: 64px;"></i>
                <h4 class="mt-3 text-muted">Aucune promotion enregistrée</h4>
                <p class="text-muted mb-4">
                    Aucune promotion n'a encore été effectuée pour cette session.<br>
                    Utilisez la réinscription intelligente pour commencer.
                </p>
                <a href="{{ route('students.smart_reenrollment') }}" class="btn btn-primary">
                    <i class="icon-users-plus mr-2"></i>Commencer une Réinscription
                </a>
            </div>
            @endif
        </div>
    </div>

@endsection

@section('scripts')
    <script>
        /* Réinitialiser individuellement */
        $('.promotion-reset').on('click', function () {
            let pid = $(this).data('id');
            if (confirm('Êtes-vous sûr de vouloir continuer ?')){
                $('form#promotion-reset-'+pid).submit();
            }
            return false;
        });

        /* Réinitialiser toutes les promotions */
        $('#promotion-reset-all').on('click', function () {
            if (confirm('Êtes-vous sûr de vouloir continuer ?')){
                $.ajax({
                    url:"{{ route('students.promotion_reset_all') }}",
                    type:'DELETE',
                    data:{ '_token' : $('#csrf-token').attr('content') },
                    success:function (resp) {
                        $('table#promotions-list > tbody').fadeOut().remove();
                        flash({msg : resp.msg, type : 'success'});
                    }
                })
            }
            return false;
        })
    </script>
@endsection
