
<?php $__env->startSection('page_title', 'Gérer les promotions'); ?>
<?php $__env->startSection('content'); ?>

    
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">Réinscription Intelligente</h6>
                    <p class="card-text text-muted">Réinscrire des élèves avec promotion automatique</p>
                    <a href="<?php echo e(route('students.smart_reenrollment')); ?>" class="btn btn-primary">
                        <i class="icon-users-plus mr-2"></i>Nouvelle Réinscription
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">Réinitialiser les Promotions</h6>
                    <p class="card-text text-muted">Annuler toutes les promotions de la session</p>
                    <button id="promotion-reset-all" class="btn btn-danger">
                        <i class="icon-reset mr-2"></i>Réinitialiser Tout
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="card">
        <div class="card-header header-elements-inline">
            <h5 class="card-title font-weight-bold">
                <i class="icon-graduation mr-2"></i>Promotions Enregistrées
                <small class="d-block text-muted mt-1">
                    Étudiants promus de la session <span class="text-danger"><?php echo e($old_year); ?></span> à la session <span class="text-success"><?php echo e($new_year); ?></span>
                </small>
            </h5>
            <div class="header-elements">
                <span class="badge badge-primary badge-pill"><?php echo e($promotions->count()); ?> promotion(s)</span>
            </div>
        </div>

        <div class="card-body">
            <?php if($promotions->count() > 0): ?>
            <div class="table-responsive">
            <table id="promotions-list" class="table table-striped table-hover datatable-button-html5-columns">
                <thead class="bg-light">
                <tr>
                    <th width="50">N°</th>
                    <th width="60">Photo</th>
                    <th>Nom de l'Élève</th>
                    <th>Classe d'Origine</th>
                    <th>Classe de Destination</th>
                    <th width="100">Statut</th>
                    <th width="120">Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php $__currentLoopData = $promotions->sortBy('fc.name')->sortBy('student.name'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td>
                            <img class="rounded-circle" style="height: 40px; width: 40px;"
                                 src="<?php echo e($p->student->photo); ?>" alt="photo">
                        </td>
                        <td>
                            <span class="font-weight-semibold"><?php echo e($p->student->name); ?></span>
                        </td>
                        <td>
                            <span class="badge badge-secondary"><?php echo e($p->fc->name); ?></span>
                            <small class="d-block text-muted"><?php echo e($p->fs->name); ?></small>
                        </td>
                        <td>
                            <span class="badge badge-primary"><?php echo e($p->tc->name); ?></span>
                            <small class="d-block text-muted"><?php echo e($p->ts->name); ?></small>
                        </td>
                        <td>
                            <?php if($p->status === 'P'): ?>
                                <span class="badge badge-success">
                                    <i class="icon-checkmark3 mr-1"></i>Promu
                                </span>
                            <?php elseif($p->status === 'D'): ?>
                                <span class="badge badge-warning">
                                    <i class="icon-cross mr-1"></i>Non promu
                                </span>
                            <?php else: ?>
                                <span class="badge badge-info">
                                    <i class="icon-graduation mr-1"></i>Diplômé
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <button data-id="<?php echo e($p->id); ?>" class="btn btn-sm btn-outline-danger promotion-reset"
                                    title="Réinitialiser cette promotion">
                                <i class="icon-reset"></i>
                            </button>
                            <form id="promotion-reset-<?php echo e($p->id); ?>" method="post" action="<?php echo e(route('students.promotion_reset', $p->id)); ?>">
                                <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="icon-graduation text-muted" style="font-size: 64px;"></i>
                <h4 class="mt-3 text-muted">Aucune promotion enregistrée</h4>
                <p class="text-muted mb-4">
                    Aucune promotion n'a encore été effectuée pour cette session.<br>
                    Utilisez la réinscription intelligente pour commencer.
                </p>
                <a href="<?php echo e(route('students.smart_reenrollment')); ?>" class="btn btn-primary">
                    <i class="icon-users-plus mr-2"></i>Commencer une Réinscription
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        /* Réinitialiser individuellement */
        $('.promotion-reset').on('click', function () {
            let pid = $(this).data('id');
            if (confirm('Êtes-vous sûr de vouloir continuer ?')){
                $('form#promotion-reset-'+pid).submit();
            }
            return false;
        });

        /* Réinitialiser toutes les promotions */
        $('#promotion-reset-all').on('click', function () {
            if (confirm('Êtes-vous sûr de vouloir continuer ?')){
                $.ajax({
                    url:"<?php echo e(route('students.promotion_reset_all')); ?>",
                    type:'DELETE',
                    data:{ '_token' : $('#csrf-token').attr('content') },
                    success:function (resp) {
                        $('table#promotions-list > tbody').fadeOut().remove();
                        flash({msg : resp.msg, type : 'success'});
                    }
                })
            }
            return false;
        })
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/students/promotion/reset.blade.php ENDPATH**/ ?>