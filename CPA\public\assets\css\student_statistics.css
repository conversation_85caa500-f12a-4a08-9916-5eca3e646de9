/**
 * Styles modernes pour les statistiques détaillées des étudiants
 */

/* Variables CSS pour la cohérence */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --info-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);

    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
}

/* Container principal des statistiques */
.statistics-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.statistics-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    opacity: 0.3;
}

/* En-tête des statistiques */
.statistics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    position: relative;
    z-index: 1;
}

.statistics-title {
    font-size: 2rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.statistics-controls {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.action-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.title-underline {
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
    margin-top: 8px;
    animation: expandWidth 1s ease-out;
}

@keyframes expandWidth {
    0% { width: 0; }
    100% { width: 100%; }
}

.class-selector {
    min-width: 220px;
    padding: 12px 16px;
    border: 2px solid transparent;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-weight: 500;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.class-selector:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.export-btn {
    background: var(--success-gradient);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition);
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.export-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.export-btn:hover::before {
    left: 100%;
}

.export-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--card-shadow-hover);
}

.refresh-btn {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 12px 16px;
    border-radius: 12px;
    transition: var(--transition);
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.refresh-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.refresh-btn:hover::before {
    left: 100%;
}

.refresh-btn:hover {
    transform: translateY(-3px) rotate(180deg);
    box-shadow: var(--card-shadow-hover);
}

/* Informations générales */
.statistics-info {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--card-shadow);
    position: relative;
    z-index: 1;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    text-align: center;
    padding: 25px 20px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.info-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: var(--transition);
}

.info-icon i {
    font-size: 1.5rem;
    color: white;
}

.info-item:hover .info-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.info-item:hover::before {
    transform: scaleX(1);
}

.info-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

.info-label {
    font-size: 0.95rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1.8rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Grille des statistiques */
.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 25px;
    position: relative;
    z-index: 1;
}

/* Cartes de statistiques */
.stat-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--card-shadow-hover);
}

.stat-card-header {
    padding: 20px 25px;
    background: var(--primary-gradient);
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    position: relative;
    overflow: hidden;
}

.stat-card-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1));
    transform: translateX(100px);
    transition: transform 0.6s ease;
}

.stat-card:hover .stat-card-header::after {
    transform: translateX(-100px);
}

.stat-card-body {
    padding: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

/* Tableaux de statistiques */
.stat-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
    background: transparent;
}

.stat-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    font-weight: 700;
    color: #495057;
    border: none;
    text-align: left;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-table td {
    padding: 16px 20px;
    border: none;
    vertical-align: middle;
    position: relative;
}

.stat-table tbody tr {
    transition: var(--transition);
    position: relative;
}

.stat-table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.stat-table tbody tr:hover::before {
    width: 4px;
}

.stat-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.stat-table tbody tr:last-child td {
    border-bottom: none;
}

/* Colonnes spécifiques */
.category-column {
    font-weight: 500;
    color: #495057;
}

.count-column {
    text-align: center;
    font-weight: 600;
    color: #007bff;
}

.percentage-column {
    text-align: center;
    font-weight: 500;
}

/* Barres de progression pour les pourcentages */
.progress-bar-container {
    position: relative;
    background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
    border-radius: 15px;
    height: 28px;
    overflow: hidden;
    margin-top: 8px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: var(--success-gradient);
    border-radius: 15px;
    transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.85rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    z-index: 2;
}

/* Couleurs spécifiques par type de statistique */
.stat-card.genre .stat-card-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stat-card.genre .progress-bar {
    background: linear-gradient(90deg, #ff6b6b 0%, #ee5a24 100%);
}

.stat-card.statut .stat-card-header {
    background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.stat-card.statut .progress-bar {
    background: linear-gradient(90deg, #4834d4 0%, #686de0 100%);
}

.stat-card.type-etudiant .stat-card-header {
    background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
}

.stat-card.type-etudiant .progress-bar {
    background: linear-gradient(90deg, #00d2d3 0%, #54a0ff 100%);
}

.stat-card.statut-academique .stat-card-header {
    background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);
}

.stat-card.statut-academique .progress-bar {
    background: linear-gradient(90deg, #ff9ff3 0%, #f368e0 100%);
}

.stat-card.religion .stat-card-header {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stat-card.religion .progress-bar {
    background: linear-gradient(90deg, #feca57 0%, #ff9ff3 100%);
}

.stat-card.tranche-age .stat-card-header {
    background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.stat-card.tranche-age .progress-bar {
    background: linear-gradient(90deg, #48dbfb 0%, #0abde3 100%);
}

/* États de chargement */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: var(--border-radius);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 6px solid transparent;
    border-top: 6px solid #667eea;
    border-right: 6px solid #764ba2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-overlay::after {
    content: 'Chargement des statistiques...';
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Messages d'état */
.no-data-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    margin: 20px 0;
}

/* Responsive */
@media (max-width: 1200px) {
    .statistics-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}

@media (max-width: 768px) {
    .statistics-container {
        padding: 20px;
        margin-bottom: 20px;
    }

    .statistics-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .statistics-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
        padding: 20px;
    }

    .statistics-title {
        font-size: 1.5rem;
        text-align: center;
    }

    .statistics-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .control-group {
        align-items: center;
    }

    .class-selector {
        min-width: auto;
        width: 100%;
    }

    .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .info-item {
        padding: 20px 15px;
    }

    .info-icon {
        width: 50px;
        height: 50px;
    }

    .info-icon i {
        font-size: 1.2rem;
    }

    .stat-table th,
    .stat-table td {
        padding: 12px 15px;
        font-size: 0.9rem;
    }

    .stat-card-header {
        padding: 15px 20px;
        font-size: 1.1rem;
    }

    .progress-bar-container {
        height: 24px;
    }

    .count-badge {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .statistics-container {
        padding: 15px;
        margin-bottom: 15px;
    }

    .statistics-header {
        padding: 15px;
    }

    .statistics-title {
        font-size: 1.3rem;
    }

    .statistics-info {
        padding: 20px;
    }

    .info-item {
        padding: 15px 10px;
        gap: 8px;
    }

    .info-icon {
        width: 40px;
        height: 40px;
    }

    .info-icon i {
        font-size: 1rem;
    }

    .info-value {
        font-size: 1.4rem;
    }

    .stat-card-header {
        padding: 12px 15px;
        font-size: 1rem;
    }

    .stat-table th,
    .stat-table td {
        padding: 8px 10px;
        font-size: 0.8rem;
    }

    .progress-bar-container {
        height: 20px;
    }

    .progress-text {
        font-size: 0.75rem;
    }

    .count-badge {
        padding: 3px 6px;
        font-size: 0.75rem;
    }

    .export-btn,
    .refresh-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .class-selector {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    /* Masquer les particules sur mobile pour les performances */
    .particles-container {
        display: none;
    }
}

/* Animations d'entrée */
.stat-card {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.stat-card:nth-child(5) { animation-delay: 0.5s; }
.stat-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Animation de pulsation pour les éléments importants */
.statistics-title {
    animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.1); }
}

/* Animation flottante pour les cartes d'info */
.info-item {
    animation: float 6s ease-in-out infinite;
}

.info-item:nth-child(1) { animation-delay: 0s; }
.info-item:nth-child(2) { animation-delay: 2s; }
.info-item:nth-child(3) { animation-delay: 4s; }

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Effets de particules */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 0;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Effets de hover avancés */
.header-glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5) !important;
    animation: headerPulse 1s ease-in-out infinite !important;
}

@keyframes headerPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.progress-pulse {
    animation: progressGlow 1s ease-in-out infinite !important;
}

@keyframes progressGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        filter: brightness(1);
    }
    50% {
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.6);
        filter: brightness(1.1);
    }
}

/* Badges pour les nombres */
.count-badge {
    display: inline-block;
    padding: 6px 12px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    transition: var(--transition);
}

.count-badge:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Amélioration des colonnes */
.category-column {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.count-column {
    text-align: center;
}

.percentage-column {
    min-width: 150px;
}

/* Effet de brillance sur les cartes */
.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.stat-card:hover::after {
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

/* Icônes pour les types de statistiques */
.stat-card-header::before {
    margin-right: 8px;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
}

.stat-card.genre .stat-card-header::before {
    content: "\f228"; /* fa-users */
}

.stat-card.statut .stat-card-header::before {
    content: "\f02e"; /* fa-flag */
}

.stat-card.type-etudiant .stat-card-header::before {
    content: "\f19d"; /* fa-graduation-cap */
}

.stat-card.statut-academique .stat-card-header::before {
    content: "\f091"; /* fa-trophy */
}

.stat-card.religion .stat-card-header::before {
    content: "\f6e7"; /* fa-pray */
}

.stat-card.tranche-age .stat-card-header::before {
    content: "\f1ae"; /* fa-child */
}
