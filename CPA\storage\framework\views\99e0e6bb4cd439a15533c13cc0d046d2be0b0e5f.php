<script>

    function getLGA(state_id){
        var url = '<?php echo e(route('get_lga', [':id'])); ?>';
        url = url.replace(':id', state_id);
        var lga = $('#lga_id');

        $.ajax({
            dataType: 'json',
            url: url,
            success: function (resp) {
                //console.log(resp);
                lga.empty();
                $.each(resp, function (i, data) {
                    lga.append($('<option>', {
                        value: data.id,
                        text: data.name
                    }));
                });

            }
        })
    }

    function getClassSections(class_id, destination){
        var url = '<?php echo e(route('get_class_sections', [':id'])); ?>';
        url = url.replace(':id', class_id);
        var section = destination ? $(destination) : $('#section_id');

        $.ajax({
            dataType: 'json',
            url: url,
            success: function (resp) {
                //console.log(resp);
                section.empty();
                $.each(resp, function (i, data) {
                    section.append($('<option>', {
                        value: data.id,
                        text: data.name
                    }));
                });

            }
        })
    }

    function getClassSubjects(class_id){
        var url = '<?php echo e(route('get_class_subjects', [':id'])); ?>';
        url = url.replace(':id', class_id);
        var section = $('#section_id');
        var subject = $('#subject_id');

        $.ajax({
            dataType: 'json',
            url: url,
            success: function (resp) {
                console.log(resp);
                section.empty();
                subject.empty();
                $.each(resp.sections, function (i, data) {
                    section.append($('<option>', {
                        value: data.id,
                        text: data.name
                    }));
                });
                $.each(resp.subjects, function (i, data) {
                    subject.append($('<option>', {
                        value: data.id,
                        text: data.name
                    }));
                });

            }
        })
    }


    

    <?php if(session('pop_error')): ?>
    pop({msg : '<?php echo e(session('pop_error')); ?>', type : 'error'});
    <?php endif; ?>

    <?php if(session('pop_warning')): ?>
    pop({msg : '<?php echo e(session('pop_warning')); ?>', type : 'warning'});
    <?php endif; ?>

 <?php if(session('pop_success')): ?>
    pop({msg : '<?php echo e(session('pop_success')); ?>', type : 'success', title: 'GREAT!!'});
    <?php endif; ?>

    <?php if(session('flash_info')): ?>
      flash({msg : '<?php echo e(session('flash_info')); ?>', type : 'info'});
    <?php endif; ?>

    <?php if(session('flash_success')): ?>
      flash({msg : '<?php echo e(session('flash_success')); ?>', type : 'success'});
    <?php endif; ?>

    <?php if(session('flash_warning')): ?>
      flash({msg : '<?php echo e(session('flash_warning')); ?>', type : 'warning'});
    <?php endif; ?>

     <?php if(session('flash_error') || session('flash_danger')): ?>
      flash({msg : '<?php echo e(session('flash_error') ?: session('flash_danger')); ?>', type : 'danger'});
    <?php endif; ?>

    

    function pop(data){
        swal({
            title: data.title ? data.title : 'Oops...',
            text: data.msg,
            icon: data.type
        });
    }

    function flash(data){
        new PNotify({
            text: data.msg,
            type: data.type,
            hide : data.type !== "danger"
        });
    }

    function confirmDelete(id) {
        swal({
            title: "Are you sure?",
            text: "Once deleted, you will not be able to recover this item!",
            icon: "warning",
            buttons: true,
            dangerMode: true
        }).then(function(willDelete){
            if (willDelete) {
             $('form#item-delete-'+id).submit();
            }
        });
    }

    function confirmReset(id) {
        swal({
            title: "Are you sure?",
            text: "This will reset this item to default state",
            icon: "warning",
            buttons: true,
            dangerMode: true
        }).then(function(willDelete){
            if (willDelete) {
             $('form#item-reset-'+id).submit();
            }
        });
    }

    $('form#ajax-reg').on('submit', function(ev){
        ev.preventDefault();
        submitForm($(this), 'store');
        $('#ajax-reg-t-0').get(0).click();
    });

    $('form.ajax-pay').on('submit', function(ev){
        ev.preventDefault();
        submitForm($(this), 'store');

//        Retrieve IDS
        var form_id = $(this).attr('id');
        var td_amt = $('td#amt-'+form_id);
        var td_amt_paid = $('td#amt_paid-'+form_id);
        var td_bal = $('td#bal-'+form_id);
        var input = $('#val-'+form_id);

        // Get Values
        var amt = parseInt(td_amt.data('amount'));
        var amt_paid = parseInt(td_amt_paid.data('amount'));
        var amt_input = parseInt(input.val());

//        Update Values
        amt_paid = amt_paid + amt_input;
        var bal = amt - amt_paid;

        td_bal.text(''+bal);
        td_amt_paid.text(''+amt_paid).data('amount', ''+amt_paid);
        input.attr('max', bal);
        bal < 1 ? $('#'+form_id).fadeOut('slow').remove() : '';
    });

    $('form.ajax-store').on('submit', function(ev){
        ev.preventDefault();
        submitForm($(this), 'store');
        var div = $(this).data('reload');
        div ? reloadDiv(div) : '';
    });

    $('form.ajax-update').on('submit', function(ev){
        ev.preventDefault();
        submitForm($(this));
        var div = $(this).data('reload');
        div ? reloadDiv(div) : '';
    });

    $('.download-receipt').on('click', function(ev){
        ev.preventDefault();
        $.get($(this).attr('href'));
        flash({msg : '<?php echo e('Download in Progress'); ?>', type : 'info'});
    });

    function reloadDiv(div){
        var url = window.location.href;
        url = url + ' '+ div;
        $(div).load( url );
    }

    function submitForm(form, formType){
        var btn = form.find('button[type=submit]');
        disableBtn(btn);
        var ajaxOptions = {
            url:form.attr('action'),
            type:'POST',
            cache:false,
            processData:false,
            dataType:'json',
            contentType:false,
            data:new FormData(form[0])
        };
        var req = $.ajax(ajaxOptions);
        req.done(function(resp){
            resp.ok && resp.msg
               ? flash({msg:resp.msg, type:'success'})
               : flash({msg:resp.msg, type:'danger'});
            hideAjaxAlert();
            enableBtn(btn);
            formType == 'store' ? clearForm(form) : '';
            scrollTo('body');
            return resp;
        });
        req.fail(function(e){
            if (e.status == 422){
                var errors = e.responseJSON.errors;
                displayAjaxErr(errors);
            }
           if(e.status == 500){
               displayAjaxErr([e.status + ' ' + e.statusText + ' Please Check for Duplicate entry or Contact School Administrator/IT Personnel'])
           }
            if(e.status == 404){
               displayAjaxErr([e.status + ' ' + e.statusText + ' - Requested Resource or Record Not Found'])
           }
            enableBtn(btn);
            return e.status;
        });
    }

    function disableBtn(btn){
        var btnText = btn.data('text') ? btn.data('text') : 'Submitting';
        btn.prop('disabled', true).html('<i class="icon-spinner mr-2 spinner"></i>' + btnText);
    }

    function enableBtn(btn){
        var btnText = btn.data('text') ? btn.data('text') : 'Submit Form';
        btn.prop('disabled', false).html(btnText + '<i class="icon-paperplane ml-2"></i>');
    }

    function displayAjaxErr(errors){
        $('#ajax-alert').show().html(' <div class="alert alert-danger border-0 alert-dismissible" id="ajax-msg"><button type="button" class="close" data-dismiss="alert"><span>&times;</span></button></div>');
        $.each(errors, function(k, v){
            $('#ajax-msg').append('<span><i class="icon-arrow-right5"></i> '+ v +'</span><br/>');
        });
        scrollTo('body');
    }

    function scrollTo(el){
        $('html, body').animate({
            scrollTop:$(el).offset().top
        }, 2000);
    }

    function hideAjaxAlert(){
        $('#ajax-alert').hide();
    }

    function clearForm(form){
        form.find('.select, .select-search').val([]).select2({ placeholder: 'Select...'});
        form[0].reset();
    }

    // Gestion du sélecteur de session
    function loadSessionDropdown() {
        $.ajax({
            url: '<?php echo e(route("sessions.get")); ?>',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                var dropdown = $('#session-dropdown');
                var currentSession = response.current_session;

                // Vider le dropdown
                dropdown.html('<div class="dropdown-header">Changer d\'année scolaire</div><div class="dropdown-divider"></div>');

                // Ajouter les options
                $.each(response.sessions, function(index, session) {
                    var isActive = session.name === currentSession;
                    var itemClass = isActive ? 'dropdown-item active' : 'dropdown-item';
                    var checkIcon = isActive ? '<i class="icon-checkmark3 mr-2"></i>' : '<span class="mr-4"></span>';

                    dropdown.append(
                        '<a href="#" class="' + itemClass + '" data-session="' + session.name + '">' +
                        checkIcon + session.name +
                        (session.is_active ? ' <span class="badge badge-success ml-2">Défaut</span>' : '') +
                        '</a>'
                    );
                });
            },
            error: function() {
                $('#session-dropdown').html('<div class="dropdown-item text-danger">Erreur de chargement</div>');
            }
        });
    }

    function changeSession(sessionName) {
        $.ajax({
            url: '<?php echo e(route("sessions.change")); ?>',
            type: 'POST',
            data: {
                session_name: sessionName,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Mettre à jour le badge
                    $('#current-session-badge').text(response.session_name);

                    // Recharger la page pour appliquer les filtres
                    location.reload();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function() {
                alert('Erreur lors du changement de session');
            }
        });
    }

    $(document).ready(function() {
        // Charger le dropdown au clic
        $('#session-dropdown').parent().on('show.bs.dropdown', function() {
            loadSessionDropdown();
        });

        // Gérer le clic sur une session
        $(document).on('click', '#session-dropdown .dropdown-item[data-session]', function(e) {
            e.preventDefault();
            var sessionName = $(this).data('session');
            changeSession(sessionName);
        });
    });


</script><?php /**PATH G:\CPadv\CPA\resources\views/partials/js/custom_js.blade.php ENDPATH**/ ?>