
<?php $__env->startSection('page_title', 'Informations sur l\'étudiant - '.$my_class->name); ?>

<?php $__env->startSection('page_styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('assets/css/inline_editing.css')); ?>">
<style>
    .editing input, .editing select {
        width: 100%;
        padding: 8px;
        box-sizing: border-box;
        border: 1px solid #4caf50;
        border-radius: 4px;
    }
    .editing select {
        height: 38px;
    }
    .editing .datepicker {
        width: 100%;
    }
    .save-indicator {
        margin-left: 5px;
        display: none;
    }
    .save-success {
        color: green;
    }
    .save-error {
        color: red;
    }
</style>
<?php $__env->stopSection(); ?>



<?php $__env->startSection('content'); ?>

    <div class="card">
        <div class="card-header header-elements-inline">
            <h6 class="card-title">Liste des étudiants</h6>
            <?php echo Qs::getPanelOptions(); ?>

        </div>

        <div class="card-body">
            <ul class="nav nav-tabs nav-tabs-highlight">
                <li class="nav-item"><a href="#all-students" class="nav-link active" data-toggle="tab">Tous les étudiants de la classe <?php echo e($my_class->name); ?></a></li>
                <li class="nav-item"><a href="#all-students-all-classes" class="nav-link" data-toggle="tab">Tous les étudiants de toutes les classes</a></li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">Sections</a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="#s<?php echo e($s->id); ?>" class="dropdown-item" data-toggle="tab"><?php echo e($my_class->name.' '.$s->name); ?></a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">Filtrer par classe</a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <?php $__currentLoopData = $my_classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="#c<?php echo e($c->id); ?>" class="dropdown-item" data-toggle="tab"><?php echo e($c->name); ?></a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </li>
                <li class="nav-item ml-auto">
                    <a href="<?php echo e(route('students.list_all')); ?>" class="nav-link bg-info text-white">
                        <i class="icon-list3 mr-2"></i> Voir tous les étudiants
                    </a>
                </li>
            </ul>

            <div class="tab-content">
                <div class="tab-pane fade show active" id="all-students">
                    <div class="table-responsive">
                    <table class="table datatable-button-html5-columns">
                        <thead>
                        <tr>
                            <th>N°</th>
                            <th>Photo</th>
                            <th>Nom</th>
                            <th>N° d'admission</th>
                            <th>Classe/Section</th>
                            <th>Date de naissance</th>
                            <th>Âge</th>
                            <th>Adresse</th>
                            <th>Religion</th>
                            <th>Statut</th>
                            <th>Type</th>
                            <th>Statut académique</th>
                            <th>Père/Tuteur</th>
                            <th>Profession père</th>
                            <th>Mère/Tutrice</th>
                            <th>Profession mère</th>
                            <th>Téléphone</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="number-column"><?php echo e($loop->iteration); ?></td>
                                <td class="photo-column"><img class="rounded-circle" src="<?php echo e($s->user->photo); ?>" alt="photo"></td>
                                <td class="editable editable-cell" data-field="name" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->name); ?>"><?php echo e($s->user->name); ?></td>
                                <td class="editable editable-cell" data-field="adm_no" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->adm_no); ?>"><?php echo e($s->adm_no); ?></td>
                                <td><?php echo e($my_class->name.' '.$s->section->name); ?></td>
                                <td class="editable editable-cell" data-field="dob" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->dob); ?>"><?php echo e($s->user->dob); ?></td>
                                <td class="age-display age-column" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-dob="<?php echo e($s->user->dob); ?>">
                                    <?php if($s->user->dob): ?>
                                        <?php echo e(\App\Helpers\Qs::calculateAge($s->user->dob)); ?>

                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td class="editable editable-cell" data-field="address" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->address); ?>"><?php echo e($s->user->address); ?></td>
                                <td class="editable editable-cell" data-field="religion" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->religion); ?>"><?php echo e($s->user->religion); ?></td>
                                <td class="editable editable-cell" data-field="status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->status ?? 'Normal'); ?>"><?php echo e($s->user->status ?? 'Normal'); ?></td>
                                <td class="editable editable-cell" data-field="student_type" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->student_type ?? 'Nouveau'); ?>"><?php echo e($s->user->student_type ?? 'Nouveau'); ?></td>
                                <td class="editable editable-cell" data-field="academic_status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->academic_status ?? 'Passant'); ?>"><?php echo e($s->user->academic_status ?? 'Passant'); ?></td>
                                <td class="editable editable-cell" data-field="nom_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->nom_p); ?>"><?php echo e($s->user->nom_p); ?></td>
                                <td class="editable editable-cell" data-field="prof_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->prof_p); ?>"><?php echo e($s->user->prof_p); ?></td>
                                <td class="editable editable-cell" data-field="nom_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->nom_m); ?>"><?php echo e($s->user->nom_m); ?></td>
                                <td class="editable editable-cell" data-field="prof_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->prof_m); ?>"><?php echo e($s->user->prof_m); ?></td>
                                <td class="editable editable-cell" data-field="phone" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-original-value="<?php echo e($s->user->phone); ?>"><?php echo e($s->user->phone); ?></td>
                                <td class="text-center">
                                    <div class="list-icons">
                                        <div class="dropdown">
                                            <a href="#" class="list-icons-item" data-toggle="dropdown">
                                                <i class="icon-menu9"></i>
                                            </a>

                                            <div class="dropdown-menu dropdown-menu-left">
                                                <a href="<?php echo e(route('students.show', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-eye"></i> Voir le profil</a>
                                                <?php if(Qs::userIsTeamSA()): ?>
                                                    <a href="<?php echo e(route('students.edit', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-pencil"></i> Modifier</a>
                                                    <a href="<?php echo e(route('st.reset_pass', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-lock"></i> Réinitialiser le mot de passe</a>
                                                <?php endif; ?>
                                                <a target="_blank" href="<?php echo e(route('marks.year_selector', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-check"></i> Fiche de notes</a>

                                                
                                                <?php if(Qs::userIsSuperAdmin()): ?>
                                                    <a id="<?php echo e(Qs::hash($s->user->id)); ?>" onclick="confirmDelete(this.id)" href="#" class="dropdown-item"><i class="icon-trash"></i> Supprimer</a>
                                                    <form method="post" id="item-delete-<?php echo e(Qs::hash($s->user->id)); ?>" action="<?php echo e(route('students.destroy', Qs::hash($s->user->id))); ?>" class="hidden"><?php echo csrf_field(); ?> <?php echo method_field('delete'); ?></form>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                    </div>
                </div>

                <div class="tab-pane fade" id="all-students-all-classes">
                    <div class="table-responsive">
                    <table class="table datatable-button-html5-columns">
                        <thead>
                        <tr>
                            <th>N°</th>
                            <th>Photo</th>
                            <th>Nom</th>
                            <th>N° d'admission</th>
                            <th>Classe/Section</th>
                            <th>Date de naissance</th>
                            <th>Âge</th>
                            <th>Adresse</th>
                            <th>Statut</th>
                            <th>Type</th>
                            <th>Statut académique</th>
                            <th>Père/Tuteur</th>
                            <th>Profession père</th>
                            <th>Mère/Tutrice</th>
                            <th>Profession mère</th>
                            <th>Téléphone</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $__currentLoopData = $all_students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($loop->iteration); ?></td>
                                <td><img class="rounded-circle" style="height: 40px; width: 40px;" src="<?php echo e($s->user->photo); ?>" alt="photo"></td>
                                <td><?php echo e($s->user->name); ?></td>
                                <td><?php echo e($s->adm_no); ?></td>
                                <td><?php echo e($s->my_class->name.' '.$s->section->name); ?></td>
                                <td class="editable" data-field="dob" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->dob); ?></td>
                                <td class="age-display" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->age); ?></td>
                                <td class="editable" data-field="address" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->address); ?></td>
                                <td class="editable" data-field="status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->status ?? 'Normal'); ?></td>
                                <td class="editable" data-field="student_type" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->student_type ?? 'Nouveau'); ?></td>
                                <td class="editable" data-field="academic_status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->academic_status ?? 'Passant'); ?></td>
                                <td class="editable" data-field="nom_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->nom_p); ?></td>
                                <td class="editable" data-field="prof_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->prof_p); ?></td>
                                <td class="editable" data-field="nom_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->nom_m); ?></td>
                                <td class="editable" data-field="prof_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->prof_m); ?></td>
                                <td class="editable" data-field="phone" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->phone); ?></td>
                                <td class="text-center">
                                    <div class="list-icons">
                                        <div class="dropdown">
                                            <a href="#" class="list-icons-item" data-toggle="dropdown">
                                                <i class="icon-menu9"></i>
                                            </a>

                                            <div class="dropdown-menu dropdown-menu-left">
                                                <a href="<?php echo e(route('students.show', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-eye"></i> Voir le profil</a>
                                                <?php if(Qs::userIsTeamSA()): ?>
                                                    <a href="<?php echo e(route('students.edit', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-pencil"></i> Modifier</a>
                                                    <a href="<?php echo e(route('st.reset_pass', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-lock"></i> Réinitialiser le mot de passe</a>
                                                <?php endif; ?>
                                                <a target="_blank" href="<?php echo e(route('marks.year_selector', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-check"></i> Fiche de notes</a>

                                                
                                                <?php if(Qs::userIsSuperAdmin()): ?>
                                                    <a id="<?php echo e(Qs::hash($s->user->id)); ?>" onclick="confirmDelete(this.id)" href="#" class="dropdown-item"><i class="icon-trash"></i> Supprimer</a>
                                                    <form method="post" id="item-delete-<?php echo e(Qs::hash($s->user->id)); ?>" action="<?php echo e(route('students.destroy', Qs::hash($s->user->id))); ?>" class="hidden"><?php echo csrf_field(); ?> <?php echo method_field('delete'); ?></form>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                    </div>
                </div>

                <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $se): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="tab-pane fade" id="s<?php echo e($se->id); ?>">
                        <div class="table-responsive">
                        <table class="table datatable-button-html5-columns">
                            <thead>
                            <tr>
                                <th>N°</th>
                                <th>Photo</th>
                                <th>Nom</th>
                                <th>N° d'admission</th>
                                <th>Classe/Section</th>
                                <th>Date de naissance</th>
                                <th>Âge</th>
                                <th>Adresse</th>
                                <th>Statut</th>
                                <th>Type</th>
                                <th>Statut académique</th>
                                <th>Père/Tuteur</th>
                                <th>Profession père</th>
                                <th>Mère/Tutrice</th>
                                <th>Profession mère</th>
                                <th>Téléphone</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $students->where('section_id', $se->id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><img class="rounded-circle" style="height: 40px; width: 40px;" src="<?php echo e($s->user->photo); ?>" alt="photo"></td>
                                    <td><?php echo e($s->user->name); ?></td>
                                    <td><?php echo e($s->adm_no); ?></td>
                                    <td><?php echo e($my_class->name.' '.$s->section->name); ?></td>
                                    <td class="editable" data-field="dob" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->dob); ?></td>
                                    <td class="age-display" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-dob="<?php echo e($s->user->dob); ?>">
                                        <?php if($s->user->dob): ?>
                                            <?php echo e(\App\Helpers\Qs::calculateAge($s->user->dob)); ?>

                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td class="editable" data-field="address" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->address); ?></td>
                                    <td class="editable" data-field="status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->status ?? 'Normal'); ?></td>
                                    <td class="editable" data-field="student_type" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->student_type ?? 'Nouveau'); ?></td>
                                    <td class="editable" data-field="academic_status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->academic_status ?? 'Passant'); ?></td>
                                    <td class="editable" data-field="nom_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->nom_p); ?></td>
                                    <td class="editable" data-field="prof_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->prof_p); ?></td>
                                    <td class="editable" data-field="nom_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->nom_m); ?></td>
                                    <td class="editable" data-field="prof_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->prof_m); ?></td>
                                    <td class="editable" data-field="phone" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->phone); ?></td>
                                    <td class="text-center">
                                        <div class="list-icons">
                                            <div class="dropdown">
                                                <a href="#" class="list-icons-item" data-toggle="dropdown">
                                                    <i class="icon-menu9"></i>
                                                </a>

                                                <div class="dropdown-menu dropdown-menu-left">
                                                    <a href="<?php echo e(route('students.show', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-eye"></i> Voir le profil</a>
                                                    <?php if(Qs::userIsTeamSA()): ?>
                                                        <a href="<?php echo e(route('students.edit', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-pencil"></i> Modifier</a>
                                                        <a href="<?php echo e(route('st.reset_pass', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-lock"></i> Réinitialiser le mot de passe</a>
                                                    <?php endif; ?>
                                                    <a target="_blank" href="<?php echo e(route('marks.year_selector', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-check"></i> Fiche de notes</a>

                                                    
                                                    <?php if(Qs::userIsSuperAdmin()): ?>
                                                        <a id="<?php echo e(Qs::hash($s->user->id)); ?>" onclick="confirmDelete(this.id)" href="#" class="dropdown-item"><i class="icon-trash"></i> Supprimer</a>
                                                        <form method="post" id="item-delete-<?php echo e(Qs::hash($s->user->id)); ?>" action="<?php echo e(route('students.destroy', Qs::hash($s->user->id))); ?>" class="hidden"><?php echo csrf_field(); ?> <?php echo method_field('delete'); ?></form>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <?php $__currentLoopData = $my_classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="tab-pane fade" id="c<?php echo e($mc->id); ?>">
                        <div class="table-responsive">
                        <table class="table datatable-button-html5-columns">
                            <thead>
                            <tr>
                                <th>N°</th>
                                <th>Photo</th>
                                <th>Nom</th>
                                <th>N° d'admission</th>
                                <th>Classe/Section</th>
                                <th>Date de naissance</th>
                                <th>Âge</th>
                                <th>Adresse</th>
                                <th>Statut</th>
                                <th>Type</th>
                                <th>Statut académique</th>
                                <th>Père/Tuteur</th>
                                <th>Profession père</th>
                                <th>Mère/Tutrice</th>
                                <th>Profession mère</th>
                                <th>Téléphone</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $all_students->where('my_class_id', $mc->id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><img class="rounded-circle" style="height: 40px; width: 40px;" src="<?php echo e($s->user->photo); ?>" alt="photo"></td>
                                    <td><?php echo e($s->user->name); ?></td>
                                    <td><?php echo e($s->adm_no); ?></td>
                                    <td><?php echo e($mc->name.' '.$s->section->name); ?></td>
                                    <td class="editable" data-field="dob" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->dob); ?></td>
                                    <td class="age-display" data-student-id="<?php echo e(Qs::hash($s->id)); ?>" data-dob="<?php echo e($s->user->dob); ?>">
                                        <?php if($s->user->dob): ?>
                                            <?php echo e(\App\Helpers\Qs::calculateAge($s->user->dob)); ?>

                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td class="editable" data-field="address" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->address); ?></td>
                                    <td class="editable" data-field="status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->status ?? 'Normal'); ?></td>
                                    <td class="editable" data-field="student_type" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->student_type ?? 'Nouveau'); ?></td>
                                    <td class="editable" data-field="academic_status" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->academic_status ?? 'Passant'); ?></td>
                                    <td class="editable" data-field="nom_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->nom_p); ?></td>
                                    <td class="editable" data-field="prof_p" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->prof_p); ?></td>
                                    <td class="editable" data-field="nom_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->nom_m); ?></td>
                                    <td class="editable" data-field="prof_m" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->prof_m); ?></td>
                                    <td class="editable" data-field="phone" data-student-id="<?php echo e(Qs::hash($s->id)); ?>"><?php echo e($s->user->phone); ?></td>
                                    <td class="text-center">
                                        <div class="list-icons">
                                            <div class="dropdown">
                                                <a href="#" class="list-icons-item" data-toggle="dropdown">
                                                    <i class="icon-menu9"></i>
                                                </a>

                                                <div class="dropdown-menu dropdown-menu-left">
                                                    <a href="<?php echo e(route('students.show', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-eye"></i> Voir le profil</a>
                                                    <?php if(Qs::userIsTeamSA()): ?>
                                                        <a href="<?php echo e(route('students.edit', Qs::hash($s->id))); ?>" class="dropdown-item"><i class="icon-pencil"></i> Modifier</a>
                                                        <a href="<?php echo e(route('st.reset_pass', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-lock"></i> Réinitialiser le mot de passe</a>
                                                    <?php endif; ?>
                                                    <a target="_blank" href="<?php echo e(route('marks.year_selector', Qs::hash($s->user->id))); ?>" class="dropdown-item"><i class="icon-check"></i> Fiche de notes</a>

                                                    
                                                    <?php if(Qs::userIsSuperAdmin()): ?>
                                                        <a id="<?php echo e(Qs::hash($s->user->id)); ?>" onclick="confirmDelete(this.id)" href="#" class="dropdown-item"><i class="icon-trash"></i> Supprimer</a>
                                                        <form method="post" id="item-delete-<?php echo e(Qs::hash($s->user->id)); ?>" action="<?php echo e(route('students.destroy', Qs::hash($s->user->id))); ?>" class="hidden"><?php echo csrf_field(); ?> <?php echo method_field('delete'); ?></form>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset('assets/js/inline_editing.js')); ?>"></script>
<script>
    $(document).ready(function() {
        // Le système d'édition en ligne est maintenant géré par inline_editing.js
        // Configuration du token CSRF
        window.inlineEditor.options.csrfToken = '<?php echo e(csrf_token()); ?>';
        window.inlineEditor.options.saveUrl = '<?php echo e(route("ajax.update_student_field")); ?>';
    });

    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/students/list.blade.php ENDPATH**/ ?>