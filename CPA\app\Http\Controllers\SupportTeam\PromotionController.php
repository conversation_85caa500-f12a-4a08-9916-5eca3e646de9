<?php

namespace App\Http\Controllers\SupportTeam;

use App\Helpers\Qs;
use App\Http\Controllers\Controller;
use App\Models\Mark;
use App\Models\Session;
use App\Models\StudentRecord;
use App\Models\Payment;
use App\Models\PaymentRecord;
use App\Repositories\MyClassRepo;
use App\Repositories\StudentRepo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PromotionController extends Controller
{
    protected $my_class, $student;

    public function __construct(MyClassRepo $my_class, StudentRepo $student)
    {
        $this->middleware('teamSA');

        $this->my_class = $my_class;
        $this->student = $student;
    }

    public function promotion($fc = NULL, $fs = NULL, $tc = NULL, $ts = NULL)
    {
        $d['old_year'] = $old_yr = Qs::getSetting('current_session');
        $old_yr = explode('-', $old_yr);
        $d['new_year'] = ++$old_yr[0].'-'.++$old_yr[1];
        $d['my_classes'] = $this->my_class->all();
        $d['sections'] = $this->my_class->getAllSections();
        $d['selected'] = false;

        if($fc && $fs && $tc && $ts){
            $d['selected'] = true;
            $d['fc'] = $fc;
            $d['fs'] = $fs;
            $d['tc'] = $tc;
            $d['ts'] = $ts;
            $d['students'] = $sts = $this->student->getRecord(['my_class_id' => $fc, 'section_id' => $fs, 'session' => $d['old_year']])->get();

            if($sts->count() < 1){
                return redirect()->route('students.promotion')->with('flash_success', __('msg.nstp'));
            }
        }

        return view('pages.support_team.students.promotion.index', $d);
    }

    public function selector(Request $req)
    {
        return redirect()->route('students.promotion', [$req->fc, $req->fs, $req->tc, $req->ts]);
    }

    public function promote(Request $req, $fc, $fs, $tc, $ts)
    {
        $oy = Qs::getSetting('current_session'); $d = [];
        $old_yr = explode('-', $oy);
        $ny = ++$old_yr[0].'-'.++$old_yr[1];
        $students = $this->student->getRecord(['my_class_id' => $fc, 'section_id' => $fs, 'session' => $oy ])->get()->sortBy('user.name');

        if($students->count() < 1){
            return redirect()->route('students.promotion')->with('flash_danger', __('msg.srnf'));
        }

        foreach($students as $st){
            $p = 'p-'.$st->id;
            $p = $req->$p;
            if($p === 'P'){ // Promote
                $d['my_class_id'] = $tc;
                $d['section_id'] = $ts;
                $d['session'] = $ny;
            }
            if($p === 'D'){ // Don't Promote
                $d['my_class_id'] = $fc;
                $d['section_id'] = $fs;
                $d['session'] = $ny;
            }
            if($p === 'G'){ // Graduated
                $d['my_class_id'] = $fc;
                $d['section_id'] = $fs;
                $d['grad'] = 1;
                $d['grad_date'] = $oy;
            }

            $this->student->updateRecord($st->id, $d);

//            Insert New Promotion Data
            $promote['from_class'] = $fc;
            $promote['from_section'] = $fs;
            $promote['grad'] = ($p === 'G') ? 1 : 0;
            $promote['to_class'] = in_array($p, ['D', 'G']) ? $fc : $tc;
            $promote['to_section'] = in_array($p, ['D', 'G']) ? $fs : $ts;
            $promote['student_id'] = $st->user_id;
            $promote['from_session'] = $oy;
            $promote['to_session'] = $ny;
            $promote['status'] = $p;

            $this->student->createPromotion($promote);
        }
        return redirect()->route('students.promotion')->with('flash_success', __('msg.update_ok'));
    }

    public function manage()
    {
        $data['promotions'] = $this->student->getAllPromotions();
        $data['old_year'] = Qs::getCurrentSession();
        $data['new_year'] = Qs::getNextSession();

        return view('pages.support_team.students.promotion.reset', $data);
    }

    public function reset($promotion_id)
    {
        $this->reset_single($promotion_id);

        return redirect()->route('students.promotion_manage')->with('flash_success', __('msg.update_ok'));
    }

    public function reset_all()
    {
        $next_session = Qs::getNextSession();
        $where = ['from_session' => Qs::getCurrentSession(), 'to_session' => $next_session];
        $proms = $this->student->getPromotions($where);

        if ($proms->count()){
          foreach ($proms as $prom){
              $this->reset_single($prom->id);

              // Delete Marks if Already Inserted for New Session
              $this->delete_old_marks($prom->student_id, $next_session);
          }
        }

        return Qs::jsonUpdateOk();
    }

    protected function delete_old_marks($student_id, $year)
    {
        Mark::where(['student_id' => $student_id, 'year' => $year])->delete();
    }

    protected function reset_single($promotion_id)
    {
        $prom = $this->student->findPromotion($promotion_id);

        $data['my_class_id'] = $prom->from_class;
        $data['section_id'] = $prom->from_section;
        $data['session'] = $prom->from_session;
        $data['grad'] = 0;
        $data['grad_date'] = null;

        $this->student->update(['user_id' => $prom->student_id], $data);

        return $this->student->deletePromotion($promotion_id);
    }

    /**
     * Interface de réinscription intelligente
     */
    public function smartReenrollment()
    {
        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        $currentSessionObj = Session::where('name', $currentSession)->first();

        $data['current_session'] = $currentSession;
        $data['previous_session'] = $currentSessionObj ? $currentSessionObj->getPreviousSession() : null;
        $data['my_classes'] = $this->my_class->all();
        $data['sections'] = $this->my_class->getAllSections();
        $data['sessions'] = Session::getAllSorted();
        $data['selected'] = false;

        return view('pages.support_team.students.promotion.smart_reenrollment', $data);
    }

    /**
     * Sélecteur pour la réinscription intelligente
     */
    public function smartReenrollmentSelector(Request $request)
    {
        $request->validate([
            'previous_session' => 'required|string',
            'fc' => 'required|exists:my_classes,id',
            'fs' => 'required|exists:sections,id',
            'tc' => 'required|exists:my_classes,id',
            'ts' => 'required|exists:sections,id',
        ]);

        return redirect()->route('students.smart_reenrollment_show', [
            'previous_session' => $request->previous_session,
            'fc' => $request->fc,
            'fs' => $request->fs,
            'tc' => $request->tc,
            'ts' => $request->ts
        ]);
    }

    /**
     * Afficher les élèves pour la réinscription intelligente
     */
    public function smartReenrollmentShow($previous_session, $fc, $fs, $tc, $ts)
    {
        $currentSession = session('selected_school_year', Qs::getCurrentSession());

        $data['current_session'] = $currentSession;
        $data['previous_session'] = $previous_session;
        $data['fc'] = $fc;
        $data['fs'] = $fs;
        $data['tc'] = $tc;
        $data['ts'] = $ts;
        $data['my_classes'] = $this->my_class->all();
        $data['sections'] = $this->my_class->getAllSections();
        $data['selected'] = true;

        // Récupérer les élèves de la session précédente
        $students = StudentRecord::where('session', $previous_session)
            ->where('my_class_id', $fc)
            ->where('section_id', $fs)
            ->where('grad', 0)
            ->with(['user', 'my_class', 'section'])
            ->get();

        // Vérifier lesquels sont déjà réinscrits dans la session courante
        foreach ($students as $student) {
            $student->already_enrolled = StudentRecord::where('user_id', $student->user_id)
                ->where('session', $currentSession)
                ->exists();

            // Vérifier s'il y a déjà une promotion enregistrée
            $student->has_promotion = $this->student->getPromotions([
                'student_id' => $student->user_id,
                'from_session' => $previous_session,
                'to_session' => $currentSession
            ])->first();
        }

        $data['students'] = $students;

        return view('pages.support_team.students.promotion.smart_reenrollment', $data);
    }

    /**
     * Traiter la réinscription intelligente
     */
    public function processSmartReenrollment(Request $request, $previous_session, $fc, $fs, $tc, $ts)
    {
        $request->validate([
            'students' => 'required|array',
            'students.*' => 'exists:users,id'
        ]);

        $currentSession = session('selected_school_year', Qs::getCurrentSession());
        $enrolledCount = 0;
        $skippedCount = 0;
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($request->students as $studentId) {
                // Vérifier si l'élève n'est pas déjà réinscrit
                $existingRecord = StudentRecord::where('user_id', $studentId)
                    ->where('session', $currentSession)
                    ->first();

                if ($existingRecord) {
                    $skippedCount++;
                    continue;
                }

                // Récupérer l'ancien enregistrement
                $oldRecord = StudentRecord::where('user_id', $studentId)
                    ->where('session', $previous_session)
                    ->where('my_class_id', $fc)
                    ->where('section_id', $fs)
                    ->first();

                if (!$oldRecord) {
                    $errors[] = "Élève ID $studentId non trouvé dans la session précédente";
                    continue;
                }

                // Créer le nouvel enregistrement
                $newRecord = StudentRecord::create([
                    'user_id' => $studentId,
                    'my_class_id' => $tc,
                    'section_id' => $ts,
                    'session' => $currentSession,
                    'adm_no' => $oldRecord->adm_no,
                    'my_parent_id' => $oldRecord->my_parent_id,
                    'dorm_id' => $oldRecord->dorm_id,
                    'dorm_room_no' => $oldRecord->dorm_room_no,
                    'house' => $oldRecord->house,
                    'age' => $oldRecord->age,
                    'year_admitted' => $oldRecord->year_admitted,
                ]);

                // Créer l'enregistrement de promotion
                $promotionData = [
                    'student_id' => $studentId,
                    'from_class' => $fc,
                    'from_section' => $fs,
                    'to_class' => $tc,
                    'to_section' => $ts,
                    'grad' => 0,
                    'from_session' => $previous_session,
                    'to_session' => $currentSession,
                    'status' => 'P' // Promu
                ];

                $this->student->createPromotion($promotionData);

                // Créer les enregistrements de paiement pour la nouvelle session
                $this->createPaymentRecords($studentId, $tc, $currentSession);

                $enrolledCount++;
            }

            DB::commit();

            $message = "Réinscription intelligente terminée: $enrolledCount élève(s) réinscrit(s)";
            if ($skippedCount > 0) {
                $message .= ", $skippedCount élève(s) déjà inscrit(s)";
            }

            return redirect()->route('students.promotion_manage')
                ->with('flash_success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('flash_danger', 'Erreur lors de la réinscription: ' . $e->getMessage());
        }
    }

    /**
     * Créer les enregistrements de paiement pour un élève
     */
    private function createPaymentRecords($studentId, $classId, $session)
    {
        // Récupérer les paiements pour cette classe et cette session
        $payments = Payment::where('year', $session)
            ->where(function($query) use ($classId) {
                $query->where('my_class_id', $classId)
                      ->orWhereNull('my_class_id');
            })
            ->get();

        foreach ($payments as $payment) {
            PaymentRecord::create([
                'student_id' => $studentId,
                'payment_id' => $payment->id,
                'year' => $session,
                'amt_paid' => 0,
                'balance' => $payment->amount,
                'paid' => 0,
            ]);
        }
    }
}
