<?php

namespace App\Http\Middleware\Custom;

use Closure;
use Illuminate\Http\Request;
use App\Models\Session;
use App\Helpers\Qs;

class SessionManager
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Récupérer la session sélectionnée depuis la session utilisateur
        $selectedSessionName = session('selected_school_year');
        
        // Si aucune session n'est sélectionnée, utiliser la session active par défaut
        if (!$selectedSessionName) {
            $activeSession = Session::getActive();
            if ($activeSession) {
                $selectedSessionName = $activeSession->name;
                session(['selected_school_year' => $selectedSessionName]);
            } else {
                // Fallback vers la méthode existante
                $selectedSessionName = Qs::getCurrentSession();
                session(['selected_school_year' => $selectedSessionName]);
            }
        }
        
        // Injecter la session sélectionnée dans le container
        app()->instance('selected_school_year', $selectedSessionName);
        
        // Partager avec toutes les vues
        view()->share('current_session', $selectedSessionName);
        
        return $next($request);
    }
}
