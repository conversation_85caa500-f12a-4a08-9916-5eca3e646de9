@extends('layouts.master')
@section('page_title', 'Réinscription Intelligente')
@section('content')

<div class="content-wrapper">
    <div class="content-header header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-users-plus mr-2"></i> <span class="font-weight-semibold">Réinscription Intelligente</span></h4>
        </div>
    </div>

    <div class="content">
        <!-- Informations sur les sessions -->
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="icon-info22 mr-2"></i>Réinscription avec Promotion Automatique</h6>
                    <p><strong>Session actuelle :</strong> {{ $current_session }}</p>
                    @if($previous_session)
                        <p><strong>Session précédente disponible :</strong> {{ $previous_session->name }}</p>
                    @else
                        <p class="text-warning"><strong>Aucune session précédente trouvée.</strong></p>
                    @endif
                    <p class="mb-0"><em>Cette interface permet de réinscrire les élèves d'une classe vers une autre en créant automatiquement les enregistrements de promotion.</em></p>
                </div>
            </div>
        </div>

        <!-- Sélecteur de classes -->
        <div class="card">
            <div class="card-header header-elements-inline">
                <h6 class="card-title">Sélection des Classes</h6>
            </div>
            
            <div class="card-body">
                <form method="post" action="{{ route('students.smart_reenrollment_selector') }}">
                    @csrf
                    <div class="row">
                        <!-- Session précédente -->
                        <div class="col-md-12 mb-3">
                            <div class="form-group">
                                <label class="font-weight-bold">Session précédente :</label>
                                <select name="previous_session" class="form-control" required>
                                    <option value="">Sélectionner la session précédente</option>
                                    @foreach($sessions as $session)
                                        @if($session->name !== $current_session)
                                            <option value="{{ $session->name }}" 
                                                {{ (isset($previous_session) && $previous_session === $session->name) ? 'selected' : '' }}>
                                                {{ $session->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Classes d'origine -->
                        <div class="col-md-6">
                            <fieldset class="border p-3">
                                <legend class="w-auto px-2 text-primary">Classe d'Origine</legend>
                                
                                <div class="form-group">
                                    <label for="fc" class="font-weight-bold">Classe précédente :</label>
                                    <select required onchange="getClassSections(this.value, '#fs')" id="fc" name="fc" class="form-control">
                                        <option value="">Sélectionner la classe</option>
                                        @foreach($my_classes as $c)
                                            <option {{ (isset($fc) && $fc == $c->id) ? 'selected' : '' }} value="{{ $c->id }}">{{ $c->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="fs" class="font-weight-bold">Section précédente :</label>
                                    <select required id="fs" name="fs" class="form-control">
                                        @if(isset($fs) && $fs)
                                            <option value="{{ $fs }}">{{ $sections->where('id', $fs)->first()->name }}</option>
                                        @else
                                            <option value="">Sélectionner d'abord la classe</option>
                                        @endif
                                    </select>
                                </div>
                            </fieldset>
                        </div>

                        <!-- Classes de destination -->
                        <div class="col-md-6">
                            <fieldset class="border p-3">
                                <legend class="w-auto px-2 text-success">Classe de Destination</legend>
                                
                                <div class="form-group">
                                    <label for="tc" class="font-weight-bold">Nouvelle classe :</label>
                                    <select required onchange="getClassSections(this.value, '#ts')" id="tc" name="tc" class="form-control">
                                        <option value="">Sélectionner la classe</option>
                                        @foreach($my_classes as $c)
                                            <option {{ (isset($tc) && $tc == $c->id) ? 'selected' : '' }} value="{{ $c->id }}">{{ $c->name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="ts" class="font-weight-bold">Nouvelle section :</label>
                                    <select required id="ts" name="ts" class="form-control">
                                        @if(isset($ts) && $ts)
                                            <option value="{{ $ts }}">{{ $sections->where('id', $ts)->first()->name }}</option>
                                        @else
                                            <option value="">Sélectionner d'abord la classe</option>
                                        @endif
                                    </select>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="icon-search4 mr-2"></i>Rechercher les Élèves
                        </button>
                    </div>
                </form>
            </div>
        </div>

        @if(isset($selected) && $selected && isset($students))
        <!-- Résultats -->
        <div class="card">
            <div class="card-header header-elements-inline">
                <h6 class="card-title">
                    Élèves à Réinscrire : 
                    <span class="text-primary">{{ $my_classes->where('id', $fc)->first()->name }} {{ $sections->where('id', $fs)->first()->name }}</span>
                    <i class="icon-arrow-right8 mx-2"></i>
                    <span class="text-success">{{ $my_classes->where('id', $tc)->first()->name }} {{ $sections->where('id', $ts)->first()->name }}</span>
                </h6>
                <div class="header-elements">
                    <span class="badge badge-info">{{ $students->count() }} élève(s) trouvé(s)</span>
                </div>
            </div>
            
            <div class="card-body">
                @if($students->count() > 0)
                <form method="post" action="{{ route('students.smart_reenrollment_process', [$previous_session, $fc, $fs, $tc, $ts]) }}">
                    @csrf
                    
                    <!-- Contrôles de sélection -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="select-all">
                                <label class="custom-control-label font-weight-bold" for="select-all">
                                    Sélectionner tous les élèves disponibles
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="submit" class="btn btn-success" id="submit-btn" disabled>
                                <i class="icon-checkmark3 mr-2"></i>Réinscrire les Élèves Sélectionnés
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="bg-light">
                                <tr>
                                    <th width="50">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="select-all-header">
                                            <label class="custom-control-label" for="select-all-header"></label>
                                        </div>
                                    </th>
                                    <th>Photo</th>
                                    <th>Nom de l'Élève</th>
                                    <th>N° Admission</th>
                                    <th>Classe Actuelle</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($students->sortBy('user.name') as $student)
                                <tr class="{{ $student->already_enrolled ? 'table-warning' : '' }}">
                                    <td>
                                        @if(!$student->already_enrolled)
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input student-checkbox" 
                                                       id="student-{{ $student->user_id }}" 
                                                       name="students[]" 
                                                       value="{{ $student->user_id }}">
                                                <label class="custom-control-label" for="student-{{ $student->user_id }}"></label>
                                            </div>
                                        @else
                                            <i class="icon-checkmark3 text-success"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <img class="rounded-circle" style="height: 40px; width: 40px;" 
                                             src="{{ $student->user->photo }}" alt="photo">
                                    </td>
                                    <td>
                                        <span class="font-weight-semibold">{{ $student->user->name }}</span>
                                    </td>
                                    <td>{{ $student->adm_no ?? 'N/A' }}</td>
                                    <td>{{ $student->my_class->name }} - {{ $student->section->name }}</td>
                                    <td>
                                        @if($student->already_enrolled)
                                            <span class="badge badge-warning">
                                                <i class="icon-checkmark3 mr-1"></i>Déjà inscrit
                                            </span>
                                        @elseif($student->has_promotion)
                                            <span class="badge badge-info">
                                                <i class="icon-history mr-1"></i>Promotion existante
                                            </span>
                                        @else
                                            <span class="badge badge-success">
                                                <i class="icon-plus mr-1"></i>Disponible
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </form>
                @else
                <div class="text-center py-4">
                    <i class="icon-info22 text-muted" style="font-size: 48px;"></i>
                    <h5 class="mt-3 text-muted">Aucun élève trouvé</h5>
                    <p class="text-muted">Aucun élève n'a été trouvé dans la classe et section sélectionnées pour la session précédente.</p>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>

<script>
$(document).ready(function() {
    // Charger les sections quand une classe est sélectionnée
    $('#fc, #tc').change(function() {
        var classId = $(this).val();
        var targetSelect = $(this).attr('id') === 'fc' ? '#fs' : '#ts';
        
        if (classId) {
            getClassSections(classId, targetSelect);
        } else {
            $(targetSelect).html('<option value="">Sélectionner d\'abord la classe</option>');
        }
    });

    // Gestion de la sélection multiple
    $('#select-all, #select-all-header').change(function() {
        var isChecked = $(this).is(':checked');
        $('.student-checkbox').prop('checked', isChecked);
        updateSubmitButton();
    });

    $(document).on('change', '.student-checkbox', function() {
        updateSubmitButton();
        
        // Mettre à jour le checkbox "Sélectionner tout"
        var totalCheckboxes = $('.student-checkbox').length;
        var checkedCheckboxes = $('.student-checkbox:checked').length;
        
        $('#select-all, #select-all-header').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    function updateSubmitButton() {
        var selectedCount = $('.student-checkbox:checked').length;
        $('#submit-btn').prop('disabled', selectedCount === 0);
        
        if (selectedCount > 0) {
            $('#submit-btn').html('<i class="icon-checkmark3 mr-2"></i>Réinscrire ' + selectedCount + ' Élève(s) Sélectionné(s)');
        } else {
            $('#submit-btn').html('<i class="icon-checkmark3 mr-2"></i>Réinscrire les Élèves Sélectionnés');
        }
    }

    // Confirmation avant soumission
    $('form').submit(function(e) {
        var selectedCount = $('.student-checkbox:checked').length;
        if (selectedCount === 0) {
            e.preventDefault();
            alert('Veuillez sélectionner au moins un élève à réinscrire.');
            return false;
        }
        
        if (!confirm('Êtes-vous sûr de vouloir réinscrire ' + selectedCount + ' élève(s) ?\n\nCette action créera automatiquement :\n- Les nouveaux enregistrements d\'élèves\n- Les enregistrements de promotion\n- Les enregistrements de paiement')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>

@endsection
