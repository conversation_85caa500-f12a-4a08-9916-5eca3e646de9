@extends('layouts.master')
@section('page_title', 'Modifier l\'étudiant')
@section('content')

    <div class="card">
        <div class="card-header bg-white header-elements-inline">
            <h6 id="ajax-title" class="card-title">V<PERSON><PERSON><PERSON> remplir le formulaire ci-dessous pour modifier les informations de {{ $sr->user->name }}</h6>
            {!! Qs::getPanelOptions() !!}
        </div>

        <form method="post" enctype="multipart/form-data" class="wizard-form steps-validation ajax-update" data-reload="#ajax-title" action="{{ route('students.update', Qs::hash($sr->id)) }}" data-fouc>
            @csrf @method('PUT')
            <h6>Données personnelles</h6>
            <fieldset>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Nom complet : <span class="text-danger">*</span></label>
                            <input value="{{ $sr->user->name }}" required type="text" name="name" placeholder="Nom complet" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Adresse : <span class="text-danger">*</span></label>
                            <input value="{{ $sr->user->address }}" class="form-control" placeholder="Adresse" name="address" type="text" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="gender">Genre : <span class="text-danger">*</span></label>
                            <select class="select form-control" id="gender" name="gender" required data-fouc data-placeholder="Choisissez...">
                                <option value=""></option>
                                <option {{ ($sr->user->gender == 'Male' ? 'selected' : '') }} value="Male">Masculin</option>
                                <option {{ ($sr->user->gender == 'Female' ? 'selected' : '') }} value="Female">Féminin</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status">Statut : <span class="text-danger">*</span></label>
                            <select class="select form-control" id="status" name="status" required data-fouc data-placeholder="Choisissez...">
                                <option value=""></option>
                                <option {{ ($sr->user->status == 'Normal' ? 'selected' : '') }} value="Normal">Normal</option>
                                <option {{ ($sr->user->status == 'ADRA' ? 'selected' : '') }} value="ADRA">ADRA</option>
                                <option {{ ($sr->user->status == 'TEAM3' ? 'selected' : '') }} value="TEAM3">TEAM 3</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Téléphone parent :</label>
                            <input value="{{ $sr->user->phone }}" type="text" name="phone" class="form-control" placeholder="Téléphone du parent/tuteur" >
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Téléphone parent 2 :</label>
                            <input value="{{ $sr->user->phone2 }}" type="text" name="phone2" class="form-control" placeholder="Téléphone secondaire" >
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Date de naissance : <span class="text-danger">*</span></label>
                            <input name="dob" value="{{ $sr->user->dob }}" type="text" class="form-control date-pick" placeholder="Choisir une date..." required id="dob" onchange="calculateAge()">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Âge :</label>
                            <input name="age" value="{{ $sr->age }}" type="text" class="form-control" id="age" readonly>
                        </div>
                    </div>

                    <div class="col-md-3 cacher">
                        <div class="form-group">
                            <label for="nal_id">Nationalité : <span class="text-danger">*</span></label>
                            <select data-placeholder="Choisissez..."  name="nal_id" id="nal_id" class="select-search form-control">
                                <option value=""></option>
                                @foreach($nationals as $na)
                                    <option {{ ($sr->user->nal_id == $na->id ? 'selected' : '') }} value="{{ $na->id }}">{{ $na->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3 cacher">
                        <label for="state_id">État : <span class="text-danger">*</span></label>
                        <select onchange="getLGA(this.value)"  data-placeholder="Choisissez.." class="select-search form-control" name="state_id" id="state_id">
                            <option value=""></option>
                            @foreach($states as $st)
                                <option {{ ($sr->user->state_id == $st->id ? 'selected' : '') }} value="{{ $st->id }}">{{ $st->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-md-3 cacher">
                        <label for="lga_id">LGA : <span class="text-danger">*</span></label>
                        <select  data-placeholder="Sélectionner d'abord l'État" class="select-search form-control" name="lga_id" id="lga_id">
                            @if($sr->user->lga_id)
                                <option selected value="{{ $sr->user->lga_id }}">{{ $sr->user->lga->name}}</option>
                            @endif
                        </select>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Nom du père/tuteur :</label>
                            <input value="{{ $sr->user->nom_p }}" type="text" name="nom_p" class="form-control" placeholder="Nom du père/tuteur">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Profession du père/tuteur :</label>
                            <input value="{{ $sr->user->prof_p }}" type="text" name="prof_p" class="form-control" placeholder="Profession du père/tuteur">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Nom de la mère/tutrice :</label>
                            <input value="{{ $sr->user->nom_m }}" type="text" name="nom_m" class="form-control" placeholder="Nom de la mère/tutrice">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Profession de la mère/tutrice :</label>
                            <input value="{{ $sr->user->prof_m }}" type="text" name="prof_m" class="form-control" placeholder="Profession de la mère/tutrice">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="religion">Religion :</label>
                            <select class="select form-control" id="religion" name="religion" data-fouc data-placeholder="Choisissez...">
                                <option value=""></option>
                                <option {{ ($sr->user->religion == 'FLM') ? 'selected' : '' }} value="FLM">FLM</option>
                                <option {{ ($sr->user->religion == 'FJKM') ? 'selected' : '' }} value="FJKM">FJKM</option>
                                <option {{ ($sr->user->religion == 'Catholique') ? 'selected' : '' }} value="Catholique">Catholique</option>
                                <option {{ ($sr->user->religion == 'Adventiste') ? 'selected' : '' }} value="Adventiste">Adventiste</option>
                                <option {{ ($sr->user->religion == 'Islam') ? 'selected' : '' }} value="Islam">Islam</option>
                                <option {{ ($sr->user->religion == 'Judaïsme') ? 'selected' : '' }} value="Judaïsme">Judaïsme</option>
                                <option {{ ($sr->user->religion == 'Apokalipsy') ? 'selected' : '' }} value="Apokalipsy">Apokalipsy</option>
                                <option {{ ($sr->user->religion == 'Autres') ? 'selected' : '' }} value="Autres">Autres</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6 cacher">
                        <div class="form-group">
                            <label for="bg_id">Groupe sanguin : </label>
                            <select class="select form-control" id="bg_id" name="bg_id" data-fouc data-placeholder="Choisissez...">
                                <option value=""></option>
                                @foreach(App\Models\BloodGroup::all() as $bg)
                                    <option {{ ($sr->user->bg_id == $bg->id ? 'selected' : '') }} value="{{ $bg->id }}">{{ $bg->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="d-block">Télécharger une photo :</label>
                            <input value="{{ old('photo') }}" accept="image/*" type="file" name="photo" class="form-input-styled" data-fouc>
                            <span class="form-text text-muted">Images acceptées : jpeg, png. Taille maximale du fichier 2 Mo</span>
                        </div>
                    </div>
                </div>

            </fieldset>

            <h6>Données de l'étudiant</h6>
            <fieldset>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="my_class_id">Classe : <span class="text-danger">*</span></label>
                            <select onchange="getClassSections(this.value)" name="my_class_id" required id="my_class_id" class="form-control select-search" data-placeholder="Sélectionner la classe">
                                <option value=""></option>
                                @foreach($my_classes as $c)
                                    <option {{ $sr->my_class_id == $c->id ? 'selected' : '' }} value="{{ $c->id }}">{{ $c->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="section_id">Section : <span class="text-danger">*</span></label>
                            <select name="section_id" required id="section_id" class="form-control select" data-placeholder="Sélectionner la section">
                                <option value="{{ $sr->section_id }}">{{ $sr->section->name }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="year_admitted">Année d'admission : <span class="text-danger">*</span></label>
                            <select name="year_admitted" required data-placeholder="Choisissez..." id="year_admitted" class="select-search form-control">
                                <option value=""></option>
                                @for($y=date('Y', strtotime('- 10 years')); $y<=date('Y'); $y++)
                                    <option {{ ($sr->year_admitted == $y) ? 'selected' : '' }} value="{{ $y }}">{{ $y }}</option>
                                @endfor
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 cacher">
                        <label for="dorm_id">Dortoir : </label>
                        <select data-placeholder="Choisissez..."  name="dorm_id" id="dorm_id" class="select-search form-control">
                            <option value=""></option>
                            @foreach($dorms as $d)
                                <option {{ ($sr->dorm_id == $d->id) ? 'selected' : '' }} value="{{ $d->id }}">{{ $d->name }}</option>
                            @endforeach
                        </select>

                    </div>

                    <div class="col-md-6 cacher">
                        <div class="form-group">
                            <label>Numéro de chambre du dortoir :</label>
                            <input type="text" name="dorm_room_no" placeholder="Numéro de chambre du dortoir" class="form-control" value="{{ $sr->dorm_room_no }}">
                        </div>
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
@endsection

@section('page_scripts')
<script>
    function calculateAge() {
        var dobInput = document.getElementById('dob');
        var ageInput = document.getElementById('age');

        if (dobInput.value) {
            // Format de date YYYY-MM-DD
            var dobValue = dobInput.value;
            var dob = new Date(dobValue);

            // Vérifier si la date est valide
            if (isNaN(dob.getTime())) {
                console.error('Date de naissance invalide:', dobValue);
                ageInput.value = '';
                return;
            }

            var today = new Date();
            var age = today.getFullYear() - dob.getFullYear();

            // Ajuster l'âge si l'anniversaire n'est pas encore passé cette année
            if (today.getMonth() < dob.getMonth() ||
                (today.getMonth() === dob.getMonth() && today.getDate() < dob.getDate())) {
                age--;
            }

            ageInput.value = age;
            console.log('Age calculé:', age, 'pour la date:', dobValue);
        } else {
            ageInput.value = '';
        }
    }

    // Remplacer l'initialisation du datepicker par défaut
    $(document).ready(function() {
        // Détruire l'instance existante du datepicker s'il y en a une
        if ($.fn.datepicker && $('#dob').data('datepicker')) {
            $('#dob').datepicker('destroy');
        }

        // Initialiser le datepicker avec notre configuration personnalisée
        $('#dob').datepicker({
            format: 'yyyy-mm-dd',  // Nouveau format de date (YYYY-MM-DD)
            autoclose: true,
            todayHighlight: true,
            changeMonth: true,
            changeYear: true,
            yearRange: '-100:+0',
            endDate: new Date(),
        }).on('changeDate', function() {
            calculateAge();
        });

        // Ajouter des écouteurs d'événements supplémentaires
        $('#dob').on('change', calculateAge);
        $('#dob').on('blur', calculateAge);

        // Calculer l'âge au chargement de la page si une date est déjà sélectionnée
        calculateAge();
    });
</script>
@endsection
