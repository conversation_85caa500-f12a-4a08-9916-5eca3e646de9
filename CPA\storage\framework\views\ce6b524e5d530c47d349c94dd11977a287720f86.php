<div id="page-header" class="page-header page-header-light">
    <div class="page-header-content header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-plus-circle2 mr-2"></i> <span class="font-weight-semibold"><?php echo $__env->yieldContent('page_title'); ?></span></h4>
            <a href="#" class="header-elements-toggle text-default d-md-none"><i class="icon-more"></i></a>
        </div>

        <div class="header-elements d-none">
            <div class="d-flex justify-content-center align-items-center">
                <!-- Sélecteur d'année scolaire -->
                <div class="dropdown mr-3">
                    <button type="button" class="btn btn-link btn-float text-default dropdown-toggle" data-toggle="dropdown">
                        <i class="icon-calendar text-primary"></i>
                        <span class="font-weight-semibold">Ann<PERSON> Scolaire: <span class="badge badge-primary" id="current-session-badge"><?php echo e($current_session ?? Qs::getCurrentSession()); ?></span></span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" id="session-dropdown">
                        <div class="dropdown-header">Changer d'année scolaire</div>
                        <div class="dropdown-divider"></div>
                        <!-- Les options seront chargées via AJAX -->
                        <div class="text-center p-2">
                            <i class="icon-spinner2 spinner"></i> Chargement...
                        </div>
                    </div>
                </div>

                <?php if(Qs::userIsSuperAdmin()): ?>
                <a href="<?php echo e(route('settings')); ?>" class="btn btn-link btn-float text-default">
                    <i class="icon-cog text-primary"></i>
                    <span>Paramètres</span>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    
    
</div>
<?php /**PATH G:\CPadv\CPA\resources\views/partials/header.blade.php ENDPATH**/ ?>