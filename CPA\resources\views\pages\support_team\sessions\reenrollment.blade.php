@extends('layouts.master')
@section('page_title', 'Réinscription des Élèves')
@section('content')

<div class="content-wrapper">
    <div class="content-header header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-users mr-2"></i> <span class="font-weight-semibold">Réinscription des Élèves</span></h4>
        </div>
    </div>

    <div class="content">
        <!-- Informations sur les sessions -->
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="icon-info22 mr-2"></i>Informations</h6>
                    <p><strong>Session actuelle :</strong> {{ $current_session }}</p>
                    @if($previous_session)
                        <p><strong>Session précédente :</strong> {{ $previous_session->name }}</p>
                    @else
                        <p class="text-warning"><strong>Aucune session précédente trouvée.</strong></p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Formulaire de recherche -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Rechercher les Élèves à Réinscrire</h6>
                    </div>
                    
                    <div class="card-body">
                        <form id="search-form">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Session précédente</label>
                                        <select name="previous_session" id="previous_session" class="form-control" required>
                                            <option value="">Sélectionner...</option>
                                            @foreach($sessions as $session)
                                                @if($session->name !== $current_session)
                                                    <option value="{{ $session->name }}" 
                                                        {{ $previous_session && $session->name === $previous_session->name ? 'selected' : '' }}>
                                                        {{ $session->name }}
                                                    </option>
                                                @endif
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Classe précédente</label>
                                        <select name="class_id" id="class_id" class="form-control" required>
                                            <option value="">Sélectionner...</option>
                                            @foreach($my_classes as $class)
                                                <option value="{{ $class->id }}">{{ $class->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Section précédente</label>
                                        <select name="section_id" id="section_id" class="form-control" required>
                                            <option value="">Sélectionner d'abord une classe</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="submit" class="btn btn-primary btn-block">
                                            <i class="icon-search4 mr-2"></i>Rechercher
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Résultats de recherche -->
        <div class="row" id="results-section" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Élèves Trouvés</h6>
                        <div class="header-elements">
                            <button type="button" class="btn btn-success" id="reenroll-selected" disabled>
                                <i class="icon-checkmark3 mr-2"></i>Réinscrire les Sélectionnés
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- Formulaire de réinscription -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label>Nouvelle classe</label>
                                <select id="new_class_id" class="form-control" required>
                                    <option value="">Sélectionner...</option>
                                    @foreach($my_classes as $class)
                                        <option value="{{ $class->id }}">{{ $class->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label>Nouvelle section</label>
                                <select id="new_section_id" class="form-control" required>
                                    <option value="">Sélectionner d'abord une classe</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label>&nbsp;</label>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="select-all">
                                    <label class="custom-control-label" for="select-all">Sélectionner tout</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped" id="students-table">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="select-all-header">
                                                <label class="custom-control-label" for="select-all-header"></label>
                                            </div>
                                        </th>
                                        <th>Nom</th>
                                        <th>N° Admission</th>
                                        <th>Classe/Section</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody id="students-tbody">
                                    <!-- Les résultats seront chargés ici -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Charger les sections quand une classe est sélectionnée
    $('#class_id, #new_class_id').change(function() {
        var classId = $(this).val();
        var targetSelect = $(this).attr('id') === 'class_id' ? '#section_id' : '#new_section_id';
        
        if (classId) {
            getClassSections(classId, targetSelect);
        } else {
            $(targetSelect).html('<option value="">Sélectionner d\'abord une classe</option>');
        }
    });

    // Rechercher les élèves
    $('#search-form').submit(function(e) {
        e.preventDefault();
        
        var formData = {
            previous_session: $('#previous_session').val(),
            class_id: $('#class_id').val(),
            section_id: $('#section_id').val(),
            _token: '{{ csrf_token() }}'
        };
        
        if (!formData.previous_session || !formData.class_id || !formData.section_id) {
            alert('Veuillez remplir tous les champs');
            return;
        }
        
        $.ajax({
            url: '{{ route("sessions.search_students") }}',
            type: 'POST',
            data: formData,
            success: function(response) {
                displayStudents(response.students);
                $('#results-section').show();
            },
            error: function() {
                alert('Erreur lors de la recherche');
            }
        });
    });

    // Fonction pour afficher les élèves
    function displayStudents(students) {
        var tbody = $('#students-tbody');
        tbody.empty();
        
        if (students.length === 0) {
            tbody.append('<tr><td colspan="5" class="text-center">Aucun élève trouvé</td></tr>');
            return;
        }
        
        students.forEach(function(student) {
            var statusBadge = student.already_enrolled 
                ? '<span class="badge badge-warning">Déjà inscrit</span>'
                : '<span class="badge badge-success">Disponible</span>';
                
            var checkbox = student.already_enrolled 
                ? '<input type="checkbox" class="student-checkbox" disabled>'
                : '<input type="checkbox" class="student-checkbox" value="' + student.user_id + '">';
            
            tbody.append(
                '<tr>' +
                '<td>' + checkbox + '</td>' +
                '<td>' + student.user.name + '</td>' +
                '<td>' + (student.adm_no || 'N/A') + '</td>' +
                '<td>' + student.my_class.name + ' - ' + student.section.name + '</td>' +
                '<td>' + statusBadge + '</td>' +
                '</tr>'
            );
        });
        
        updateReenrollButton();
    }

    // Gérer la sélection
    $(document).on('change', '.student-checkbox', updateReenrollButton);
    $(document).on('change', '#select-all, #select-all-header', function() {
        var isChecked = $(this).is(':checked');
        $('.student-checkbox:not(:disabled)').prop('checked', isChecked);
        updateReenrollButton();
    });

    function updateReenrollButton() {
        var selectedCount = $('.student-checkbox:checked').length;
        $('#reenroll-selected').prop('disabled', selectedCount === 0);
    }

    // Réinscrire les élèves sélectionnés
    $('#reenroll-selected').click(function() {
        var selectedIds = [];
        $('.student-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        
        var newClassId = $('#new_class_id').val();
        var newSectionId = $('#new_section_id').val();
        
        if (!newClassId || !newSectionId) {
            alert('Veuillez sélectionner la nouvelle classe et section');
            return;
        }
        
        if (selectedIds.length === 0) {
            alert('Veuillez sélectionner au moins un élève');
            return;
        }
        
        if (!confirm('Êtes-vous sûr de vouloir réinscrire ' + selectedIds.length + ' élève(s) ?')) {
            return;
        }
        
        $.ajax({
            url: '{{ route("sessions.reenroll_students") }}',
            type: 'POST',
            data: {
                student_ids: selectedIds,
                new_class_id: newClassId,
                new_section_id: newSectionId,
                previous_session: $('#previous_session').val(),
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    // Recharger la recherche pour mettre à jour les statuts
                    $('#search-form').submit();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function() {
                alert('Erreur lors de la réinscription');
            }
        });
    });
});
</script>

@endsection
