<?php $__env->startSection('page_title', 'Réinscription Intelligente'); ?>
<?php $__env->startSection('content'); ?>

<div class="content-wrapper">
    <div class="content-header header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-users-plus mr-2"></i> <span class="font-weight-semibold">Réinscription Intelligente</span></h4>
        </div>
    </div>

    <div class="content">
        <!-- Informations sur les sessions -->
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="icon-info22 mr-2"></i>Réinscription avec Promotion Automatique</h6>
                    <p><strong>Session actuelle :</strong> <?php echo e($current_session); ?></p>
                    <?php if($previous_session): ?>
                        <p><strong>Session précédente suggérée :</strong> <?php echo e($previous_session->name); ?></p>
                    <?php else: ?>
                        <p class="text-info"><strong>Aucune session précédente automatique. Sélectionnez manuellement la session source.</strong></p>
                    <?php endif; ?>
                    <p class="mb-0"><em>Cette interface permet de réinscrire les élèves d'une classe vers une autre en créant automatiquement les enregistrements de promotion.</em></p>
                </div>
            </div>
        </div>

        <!-- Sélecteur de classes -->
        <div class="card">
            <div class="card-header header-elements-inline">
                <h6 class="card-title">Sélection des Classes</h6>
            </div>
            
            <div class="card-body">
                <form method="post" action="<?php echo e(route('students.smart_reenrollment_selector')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <!-- Session précédente -->
                        <div class="col-md-12 mb-3">
                            <div class="form-group">
                                <label class="font-weight-bold">Session précédente :</label>
                                <select name="previous_session" class="form-control" required>
                                    <option value="">Sélectionner la session source</option>
                                    <?php $__currentLoopData = $sessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($session->name !== $current_session): ?>
                                            <option value="<?php echo e($session->name); ?>"
                                                <?php echo e((isset($previous_session) && $previous_session === $session->name) ? 'selected' : ''); ?>>
                                                <?php echo e($session->name); ?>

                                                <?php if($session->name === '2024-2025'): ?>
                                                    (<?php echo e(\App\Models\StudentRecord::where('session', $session->name)->where('grad', 0)->count()); ?> élèves)
                                                <?php endif; ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <small class="form-text text-muted">
                                    Sélectionnez la session contenant les élèves à réinscrire.
                                    <strong>Astuce :</strong> La session 2024-2025 contient <?php echo e(\App\Models\StudentRecord::where('session', '2024-2025')->where('grad', 0)->count()); ?> élèves.
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Classes d'origine -->
                        <div class="col-md-6">
                            <fieldset class="border p-3">
                                <legend class="w-auto px-2 text-primary">Classe d'Origine</legend>
                                
                                <div class="form-group">
                                    <label for="fc" class="font-weight-bold">Classe précédente :</label>
                                    <select required onchange="getClassSections(this.value, '#fs')" id="fc" name="fc" class="form-control">
                                        <option value="">Sélectionner la classe</option>
                                        <?php $__currentLoopData = $my_classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option <?php echo e((isset($fc) && $fc == $c->id) ? 'selected' : ''); ?> value="<?php echo e($c->id); ?>"><?php echo e($c->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="fs" class="font-weight-bold">Section précédente :</label>
                                    <select required id="fs" name="fs" class="form-control">
                                        <?php if(isset($fs) && $fs): ?>
                                            <option value="<?php echo e($fs); ?>"><?php echo e($sections->where('id', $fs)->first()->name); ?></option>
                                        <?php else: ?>
                                            <option value="">Sélectionner d'abord la classe</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </fieldset>
                        </div>

                        <!-- Classes de destination -->
                        <div class="col-md-6">
                            <fieldset class="border p-3">
                                <legend class="w-auto px-2 text-success">Classe de Destination</legend>
                                
                                <div class="form-group">
                                    <label for="tc" class="font-weight-bold">Nouvelle classe :</label>
                                    <select required onchange="getClassSections(this.value, '#ts')" id="tc" name="tc" class="form-control">
                                        <option value="">Sélectionner la classe</option>
                                        <?php $__currentLoopData = $my_classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option <?php echo e((isset($tc) && $tc == $c->id) ? 'selected' : ''); ?> value="<?php echo e($c->id); ?>"><?php echo e($c->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="ts" class="font-weight-bold">Nouvelle section :</label>
                                    <select required id="ts" name="ts" class="form-control">
                                        <?php if(isset($ts) && $ts): ?>
                                            <option value="<?php echo e($ts); ?>"><?php echo e($sections->where('id', $ts)->first()->name); ?></option>
                                        <?php else: ?>
                                            <option value="">Sélectionner d'abord la classe</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="icon-search4 mr-2"></i>Rechercher les Élèves
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <?php if(isset($selected) && $selected && isset($students)): ?>
        <!-- Résultats -->
        <?php if($students->count() === 0): ?>
        <div class="alert alert-warning">
            <h6><i class="icon-warning mr-2"></i>Aucun élève trouvé</h6>
            <p>Aucun élève n'a été trouvé dans la classe <strong><?php echo e($my_classes->where('id', $fc)->first()->name); ?> <?php echo e($sections->where('id', $fs)->first()->name); ?></strong> pour la session <strong><?php echo e($previous_session); ?></strong>.</p>
            <p class="mb-0"><strong>Suggestions :</strong></p>
            <ul class="mb-0">
                <li>Vérifiez que la session sélectionnée contient des élèves</li>
                <li>Essayez avec la session <strong>2024-2025</strong> qui contient <?php echo e(\App\Models\StudentRecord::where('session', '2024-2025')->where('grad', 0)->count()); ?> élèves</li>
                <li>Vérifiez que la classe et section sélectionnées sont correctes</li>
            </ul>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header header-elements-inline">
                <h6 class="card-title">
                    Élèves à Réinscrire : 
                    <span class="text-primary"><?php echo e($my_classes->where('id', $fc)->first()->name); ?> <?php echo e($sections->where('id', $fs)->first()->name); ?></span>
                    <i class="icon-arrow-right8 mx-2"></i>
                    <span class="text-success"><?php echo e($my_classes->where('id', $tc)->first()->name); ?> <?php echo e($sections->where('id', $ts)->first()->name); ?></span>
                </h6>
                <div class="header-elements">
                    <span class="badge badge-info"><?php echo e($students->count()); ?> élève(s) trouvé(s)</span>
                </div>
            </div>
            
            <div class="card-body">
                <?php if($students->count() > 0): ?>
                <form method="post" action="<?php echo e(route('students.smart_reenrollment_process', [$previous_session, $fc, $fs, $tc, $ts])); ?>">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Contrôles de sélection -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="select-all">
                                <label class="custom-control-label font-weight-bold" for="select-all">
                                    Sélectionner tous les élèves disponibles
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="submit" class="btn btn-success" id="submit-btn" disabled>
                                <i class="icon-checkmark3 mr-2"></i>Réinscrire les Élèves Sélectionnés
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="bg-light">
                                <tr>
                                    <th width="50">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="select-all-header">
                                            <label class="custom-control-label" for="select-all-header"></label>
                                        </div>
                                    </th>
                                    <th>Photo</th>
                                    <th>Nom de l'Élève</th>
                                    <th>N° Admission</th>
                                    <th>Classe Actuelle</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $students->sortBy('user.name'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="<?php echo e($student->already_enrolled ? 'table-warning' : ''); ?>">
                                    <td>
                                        <?php if(!$student->already_enrolled): ?>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input student-checkbox" 
                                                       id="student-<?php echo e($student->user_id); ?>" 
                                                       name="students[]" 
                                                       value="<?php echo e($student->user_id); ?>">
                                                <label class="custom-control-label" for="student-<?php echo e($student->user_id); ?>"></label>
                                            </div>
                                        <?php else: ?>
                                            <i class="icon-checkmark3 text-success"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <img class="rounded-circle" style="height: 40px; width: 40px;"
                                             src="<?php echo e($student->user->photo ?? asset('global_assets/images/user.png')); ?>"
                                             alt="photo" onerror="this.src='<?php echo e(asset('global_assets/images/user.png')); ?>'">
                                    </td>
                                    <td>
                                        <span class="font-weight-semibold"><?php echo e($student->user->name); ?></span>
                                    </td>
                                    <td><?php echo e($student->adm_no ?? 'N/A'); ?></td>
                                    <td><?php echo e($student->my_class->name); ?> - <?php echo e($student->section->name); ?></td>
                                    <td>
                                        <?php if($student->already_enrolled): ?>
                                            <span class="badge badge-warning">
                                                <i class="icon-checkmark3 mr-1"></i>Déjà inscrit
                                            </span>
                                        <?php elseif($student->has_promotion): ?>
                                            <span class="badge badge-info">
                                                <i class="icon-history mr-1"></i>Promotion existante
                                            </span>
                                        <?php else: ?>
                                            <span class="badge badge-success">
                                                <i class="icon-plus mr-1"></i>Disponible
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </form>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="icon-info22 text-muted" style="font-size: 48px;"></i>
                    <h5 class="mt-3 text-muted">Aucun élève trouvé</h5>
                    <p class="text-muted">Aucun élève n'a été trouvé dans la classe et section sélectionnées pour la session précédente.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
$(document).ready(function() {
    // Charger les sections quand une classe est sélectionnée
    $('#fc, #tc').change(function() {
        var classId = $(this).val();
        var targetSelect = $(this).attr('id') === 'fc' ? '#fs' : '#ts';

        if (classId) {
            loadClassSections(classId, targetSelect);
        } else {
            $(targetSelect).html('<option value="">Sélectionner d\'abord la classe</option>');
        }
    });

    // Fonction pour charger les sections
    function loadClassSections(class_id, destination) {
        var url = '<?php echo e(route('get_class_sections', [':id'])); ?>';
        url = url.replace(':id', class_id);
        var section = $(destination);

        $.ajax({
            dataType: 'json',
            url: url,
            success: function (resp) {
                section.empty();
                section.append('<option value="">Sélectionner la section</option>');
                $.each(resp, function (i, data) {
                    section.append($('<option>', {
                        value: data.id,
                        text: data.name
                    }));
                });
            },
            error: function(xhr, status, error) {
                console.error('Erreur lors du chargement des sections:', error);
                section.html('<option value="">Erreur de chargement</option>');
            }
        });
    }

    // Gestion de la sélection multiple
    $('#select-all, #select-all-header').change(function() {
        var isChecked = $(this).is(':checked');
        $('.student-checkbox').prop('checked', isChecked);
        updateSubmitButton();
    });

    $(document).on('change', '.student-checkbox', function() {
        updateSubmitButton();
        
        // Mettre à jour le checkbox "Sélectionner tout"
        var totalCheckboxes = $('.student-checkbox').length;
        var checkedCheckboxes = $('.student-checkbox:checked').length;
        
        $('#select-all, #select-all-header').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    function updateSubmitButton() {
        var selectedCount = $('.student-checkbox:checked').length;
        $('#submit-btn').prop('disabled', selectedCount === 0);
        
        if (selectedCount > 0) {
            $('#submit-btn').html('<i class="icon-checkmark3 mr-2"></i>Réinscrire ' + selectedCount + ' Élève(s) Sélectionné(s)');
        } else {
            $('#submit-btn').html('<i class="icon-checkmark3 mr-2"></i>Réinscrire les Élèves Sélectionnés');
        }
    }

    // Confirmation avant soumission
    $('form').submit(function(e) {
        var selectedCount = $('.student-checkbox:checked').length;
        if (selectedCount === 0) {
            e.preventDefault();
            alert('Veuillez sélectionner au moins un élève à réinscrire.');
            return false;
        }
        
        if (!confirm('Êtes-vous sûr de vouloir réinscrire ' + selectedCount + ' élève(s) ?\n\nCette action créera automatiquement :\n- Les nouveaux enregistrements d\'élèves\n- Les enregistrements de promotion\n- Les enregistrements de paiement')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/students/promotion/smart_reenrollment.blade.php ENDPATH**/ ?>