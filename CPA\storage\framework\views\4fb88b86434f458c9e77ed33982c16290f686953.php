
<?php $__env->startSection('page_title', 'Promotion des étudiants'); ?>
<?php $__env->startSection('content'); ?>

    <div class="card">
        <div class="card-header header-elements-inline">
            <h5 class="card-title font-weight-bold">Promotion des étudiants de la session <span class="text-danger"><?php echo e($old_year); ?></span> à la session <span class="text-success"><?php echo e($new_year); ?></span></h5>
            <?php echo Qs::getPanelOptions(); ?>

        </div>

        <div class="card-body">
            <?php echo $__env->make('pages.support_team.students.promotion.selector', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>

    <?php if($selected): ?>
    <div class="card">
        <div class="card-header header-elements-inline">
            <h5 class="card-title font-weight-bold">Promouvoir les étudiants de la classe <span class="text-teal"><?php echo e($my_classes->where('id', $fc)->first()->name.' '.$sections->where('id', $fs)->first()->name); ?></span> à la classe <span class="text-purple"><?php echo e($my_classes->where('id', $tc)->first()->name.' '.$sections->where('id', $ts)->first()->name); ?></span> </h5>
            <?php echo Qs::getPanelOptions(); ?>

        </div>

        <div class="card-body">
            <?php echo $__env->make('pages.support_team.students.promotion.promote', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
    <?php endif; ?>


    

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/students/promotion/index.blade.php ENDPATH**/ ?>